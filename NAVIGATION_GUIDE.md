# 🧭 Navigation Guide - How to Access Enhanced ML Pipeline Features

## 🎯 **Where to Find the Enhanced Features**

The enhanced risk-based analysis and maintenance scheduling features are now properly integrated into your EAMS system. Here's exactly how to access them:

## 📍 **Step-by-Step Navigation**

### **1. Access Condition Monitoring**
1. **Navigate to**: `Maintenance` → `Condition Monitoring`
2. **Or use the direct URL**: `/maintenance/condition-monitoring`

### **2. Go to Predictive Tab**
1. **Click on the "Predictive" tab** in the Condition Monitoring page
2. **You'll see the Advanced Predictive Analytics dashboard**

### **3. Access ML Pipelines**
1. **In the Predictive tab, look for the tab navigation at the top**
2. **Click on "ML Pipelines" tab** (it's the last tab in the row)
3. **You'll see all the enhanced features!**

## 🎮 **What You'll Find in ML Pipelines Tab**

### **✅ Enhanced Features Available:**

#### **1. Risk Assessment System**
- **Risk Score Progress Bars**: Visual risk indicators
- **Risk Threshold Configuration**: Adjustable risk levels
- **Risk Factors Breakdown**: Detailed analysis
- **Recommendations & Mitigation Actions**

#### **2. Maintenance Scheduling**
- **Intelligent Maintenance Planning**: Risk-based scheduling
- **Priority-Based Actions**: Critical, High, Medium, Low
- **Cost Impact Analysis**: Maintenance cost estimates
- **Downtime Impact Assessment**

#### **3. Interactive Testing Panel**
- **Risk Threshold Testing**: Real-time adjustments
- **Equipment Filtering**: Multiple filter options
- **Maintenance Type Filtering**: Emergency, Predictive, etc.
- **Action Button Testing**: Interactive demonstrations

#### **4. Advanced Analytics**
- **Risk Score Trends**: Historical tracking
- **Cost-Risk Correlation**: Scatter plot analysis
- **Risk Distribution**: Doughnut chart visualization
- **Model Performance**: Accuracy tracking

## 🔍 **Visual Indicators to Look For**

### **Purple Border Cards**: Testing panels and special features
### **Color-Coded Progress Bars**:
- 🔴 **Red**: High risk (80%+)
- 🟠 **Orange**: Medium risk (60-79%)
- 🟢 **Green**: Low risk (<60%)

### **Interactive Elements**:
- **Clickable Buttons**: All action buttons
- **Dropdown Filters**: Equipment and maintenance types
- **Input Fields**: Risk threshold configuration
- **Hover Effects**: Charts and cards

## 🚀 **Quick Access Path**

```
Maintenance → Condition Monitoring → Predictive Tab → ML Pipelines Tab
```

## 🎯 **Testing the Features**

### **5-Minute Quick Test:**
1. **Find the "Interactive Testing Panel"** (purple border)
2. **Change risk threshold** from 70 to 50
3. **Watch**: More equipment shows as high risk
4. **Test equipment filters** and maintenance type filters
5. **Click action buttons** to see responses

### **What You Should See:**
- ✅ **Risk scores with progress bars**
- ✅ **Maintenance schedules with priorities**
- ✅ **Interactive charts and graphs**
- ✅ **Color-coded status indicators**
- ✅ **Professional, responsive interface**

## 🔧 **If You Can't See the Features**

### **Check These Points:**

1. **Navigation Path**:
   - Are you in `Maintenance` → `Condition Monitoring`?
   - Did you click the "Predictive" tab?
   - Did you click the "ML Pipelines" tab?

2. **Component Loading**:
   - Check browser console (F12) for errors
   - Refresh the page if needed
   - Clear browser cache

3. **File Structure**:
   - Ensure `EnhancedMLPipelines.tsx` is in the correct location
   - Verify the import path in `AdvancedPredictiveAnalytics.tsx`

## 📱 **Mobile Access**

The enhanced features are also accessible on mobile devices:
- **Responsive design** works on all screen sizes
- **Touch-friendly** controls and buttons
- **Optimized charts** for mobile viewing

## 🎉 **Success Indicators**

You've successfully accessed the enhanced features when you see:

- ✅ **"Enhanced ML Pipeline Implementation Status"** card
- ✅ **"Interactive Testing Panel"** with purple border
- ✅ **Risk-based maintenance planning** section
- ✅ **Advanced analytics dashboard** with charts
- ✅ **Professional UI** with color-coded indicators

## 📞 **Need Help?**

If you still can't see the enhanced features:

1. **Check the file structure**: Ensure all components are properly imported
2. **Verify the navigation**: Follow the exact path above
3. **Check browser console**: Look for any JavaScript errors
4. **Refresh the page**: Sometimes a simple refresh helps

The enhanced ML Pipeline features are now fully integrated and accessible through the proper navigation path in your EAMS system! 