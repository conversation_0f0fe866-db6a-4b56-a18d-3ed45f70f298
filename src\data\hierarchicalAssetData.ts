/**
 * Real Equipment Data for Hierarchical Asset Structure
 * Based on actual pump station specifications
 */

import { HierarchicalAsset, EquipmentSpecifications } from '@/types/hierarchicalAssets';

// Real equipment specifications from the provided hierarchy
export const realEquipmentSpecs = {
  // HMS Pump specifications
  hmsPump: {
    manufacturer: 'HMS',
    model: 'D200-500A-GGG(485)',
    flowRate: 172, // L/sec
    head: 80, // meters
    efficiency: 85, // percentage
    ratedPower: 250, // kW (estimated from motor)
    operatingTemperature: { min: 5, max: 40 },
    weight: 1200, // kg (estimated)
    dimensions: { length: 2000, width: 800, height: 1200 }
  } as EquipmentSpecifications,

  // ELDIN Motor specifications
  eldinMotor: {
    manufacturer: 'ELDIN',
    model: 'А355SMA4FБT2',
    ratedPower: 250, // kW
    speed: 1487, // RPM
    ratedVoltage: 380, // V (estimated)
    frequency: 50, // Hz
    efficiency: 92, // percentage
    weight: 800, // kg (estimated)
    dimensions: { length: 1200, width: 600, height: 800 }
  } as EquipmentSpecifications,

  // ROBUSCHI Priming Pump specifications
  robuschiPump: {
    manufacturer: 'ROBUSCHI',
    model: 'RVS14/M',
    flowRate: 58000, // L/hr converted to L/sec: 16.11
    pressure: 33, // m.bar vacuum
    efficiency: 75, // percentage (estimated)
    ratedPower: 15, // kW (estimated)
    weight: 300, // kg (estimated)
    dimensions: { length: 800, width: 600, height: 700 }
  } as EquipmentSpecifications,

  // El Haggar Misr Compressor specifications
  elHaggarCompressor: {
    manufacturer: 'El Haggar Misr',
    model: 'HGF 500/580',
    pressure: 8, // bar (estimated)
    flowRate: 500, // L/min (estimated)
    ratedPower: 37, // kW (estimated)
    efficiency: 80, // percentage
    weight: 450, // kg (estimated)
    dimensions: { length: 1000, width: 700, height: 900 }
  } as EquipmentSpecifications,

  // HC Water Hammer Tank specifications
  hcTank: {
    manufacturer: 'HC',
    model: 'WHT-35000',
    volume: 35000, // liters (35 m³)
    diameter: 400, // mm nozzle
    pressure: 16, // bar (estimated working pressure)
    material: 'Carbon Steel',
    weight: 2500, // kg (estimated)
    dimensions: { length: 4000, width: 2000, height: 3000 }
  } as EquipmentSpecifications,

  // Gate Valve specifications
  gateValve: {
    manufacturer: 'Generic',
    model: 'GV-100',
    diameter: 100, // mm
    pressure: 16, // bar
    material: 'Cast Iron',
    weight: 25, // kg (estimated)
    dimensions: { length: 200, width: 150, height: 300 }
  } as EquipmentSpecifications,

  // Check Valve specifications
  checkValve: {
    manufacturer: 'Generic',
    model: 'CV-150',
    diameter: 150, // mm
    pressure: 16, // bar
    material: 'Ductile Iron',
    weight: 35, // kg (estimated)
    dimensions: { length: 250, width: 180, height: 350 }
  } as EquipmentSpecifications,

  // Vibration Sensor specifications
  vibrationSensor: {
    manufacturer: 'Generic',
    model: 'VS-4-20mA',
    frequency: 10000, // Hz max
    sensitivity: 100, // mV/g
    operatingTemperature: { min: -40, max: 85 },
    weight: 0.5, // kg
    dimensions: { length: 100, width: 50, height: 30 }
  } as EquipmentSpecifications
};

// Generate hierarchical asset data
export const hierarchicalAssetData: HierarchicalAsset[] = [
  {
    id: 'ZONE-A',
    name: 'Zone A',
    type: 'zone',
    path: 'Zone A',
    level: 0,
    location: {
      zone: 'Zone A',
      station: '',
      coordinates: { x: 0, y: 0 }
    },
    assetCount: 0, // Will be calculated dynamically
    children: [
      {
        id: 'STATION-A01',
        name: 'Pump Station A01',
        type: 'station',
        parentId: 'ZONE-A',
        path: 'Zone A/Pump Station A01',
        level: 1,
        location: {
          zone: 'Zone A',
          station: 'Pump Station A01',
          coordinates: { x: 100, y: 100 }
        },
        assetCount: 0, // Will be calculated dynamically
        children: [
          // Line 1
          {
            id: 'LINE-A01-L1',
            name: 'Line 1',
            type: 'line',
            parentId: 'STATION-A01',
            path: 'Zone A/Pump Station A01/Line 1',
            level: 2,
            location: {
              zone: 'Zone A',
              station: 'Pump Station A01',
              line: 'Line 1',
              coordinates: { x: 110, y: 110 }
            },
            assetCount: 0,
            children: [
              {
                id: 'P-A01-L1-001',
                name: 'Pump P1',
                type: 'equipment',
                parentId: 'LINE-A01-L1',
                path: 'Zone A/Pump Station A01/Line 1/Pump P1',
                level: 3,
                equipmentType: 'mechanical',
                category: 'pump',
                manufacturer: 'HMS',
                model: 'D200-500A-GGG(485)',
                serialNumber: '9û6',
                assetTag: 'P-A01-L1-001',
                location: {
                  zone: 'Zone A',
                  station: 'Pump Station A01',
                  line: 'Line 1',
                  position: 'P1',
                  coordinates: { x: 111, y: 111 }
                },
                specifications: realEquipmentSpecs.hmsPump,
                status: 'operational',
                condition: 'good',
                installationDate: '2023-01-15',
                operatingHours: 8760,
                tags: ['critical', 'main-pump', 'line-1']
              },
              {
                id: 'M-A01-L1-001',
                name: 'Motor M1',
                type: 'equipment',
                parentId: 'LINE-A01-L1',
                path: 'Zone A/Pump Station A01/Line 1/Motor M1',
                level: 3,
                equipmentType: 'electrical',
                category: 'motor',
                manufacturer: 'ELDIN',
                model: 'А355SMA4FБT2',
                serialNumber: '200900079',
                assetTag: 'M-A01-L1-001',
                location: {
                  zone: 'Zone A',
                  station: 'Pump Station A01',
                  line: 'Line 1',
                  position: 'M1',
                  coordinates: { x: 112, y: 111 }
                },
                specifications: realEquipmentSpecs.eldinMotor,
                status: 'operational',
                condition: 'good',
                installationDate: '2023-01-15',
                operatingHours: 8760,
                tags: ['critical', 'main-motor', 'line-1']
              },
              {
                id: 'V-A01-L1-001',
                name: 'Gate Valve V1',
                type: 'equipment',
                parentId: 'LINE-A01-L1',
                path: 'Zone A/Pump Station A01/Line 1/Gate Valve V1',
                level: 3,
                equipmentType: 'mechanical',
                category: 'valve',
                manufacturer: 'Generic',
                model: 'GV-100',
                serialNumber: 'V-A01-L1-001',
                assetTag: 'V-A01-L1-001',
                location: {
                  zone: 'Zone A',
                  station: 'Pump Station A01',
                  line: 'Line 1',
                  position: 'V1',
                  coordinates: { x: 113, y: 111 }
                },
                specifications: realEquipmentSpecs.gateValve,
                status: 'operational',
                condition: 'good',
                installationDate: '2023-01-15',
                operatingHours: 8760,
                tags: ['valve', 'line-1']
              },
              {
                id: 'S-A01-L1-001',
                name: 'Vibration Sensor S1',
                type: 'equipment',
                parentId: 'LINE-A01-L1',
                path: 'Zone A/Pump Station A01/Line 1/Vibration Sensor S1',
                level: 3,
                equipmentType: 'instrumentation',
                category: 'sensor',
                manufacturer: 'Generic',
                model: 'VS-4-20mA',
                serialNumber: 'S-A01-L1-001',
                assetTag: 'S-A01-L1-001',
                location: {
                  zone: 'Zone A',
                  station: 'Pump Station A01',
                  line: 'Line 1',
                  position: 'S1',
                  coordinates: { x: 114, y: 111 }
                },
                specifications: realEquipmentSpecs.vibrationSensor,
                status: 'operational',
                condition: 'excellent',
                installationDate: '2023-01-15',
                operatingHours: 8760,
                tags: ['sensor', 'vibration', 'monitoring']
              }
            ]
          },
          // Line 2
          {
            id: 'LINE-A01-L2',
            name: 'Line 2',
            type: 'line',
            parentId: 'STATION-A01',
            path: 'Zone A/Pump Station A01/Line 2',
            level: 2,
            location: {
              zone: 'Zone A',
              station: 'Pump Station A01',
              line: 'Line 2',
              coordinates: { x: 120, y: 110 }
            },
            assetCount: 0,
            children: [
              {
                id: 'P-A01-L2-001',
                name: 'Pump P2',
                type: 'equipment',
                parentId: 'LINE-A01-L2',
                path: 'Zone A/Pump Station A01/Line 2/Pump P2',
                level: 3,
                equipmentType: 'mechanical',
                category: 'pump',
                manufacturer: 'HMS',
                model: 'D200-500A-GGG(485)',
                serialNumber: '9û7',
                assetTag: 'P-A01-L2-001',
                location: {
                  zone: 'Zone A',
                  station: 'Pump Station A01',
                  line: 'Line 2',
                  position: 'P2',
                  coordinates: { x: 121, y: 111 }
                },
                specifications: realEquipmentSpecs.hmsPump,
                status: 'operational',
                condition: 'good',
                installationDate: '2023-01-15',
                operatingHours: 8500,
                tags: ['critical', 'main-pump', 'line-2']
              },
              {
                id: 'M-A01-L2-001',
                name: 'Motor M2',
                type: 'equipment',
                parentId: 'LINE-A01-L2',
                path: 'Zone A/Pump Station A01/Line 2/Motor M2',
                level: 3,
                equipmentType: 'electrical',
                category: 'motor',
                manufacturer: 'ELDIN',
                model: 'А355SMA4FБT2',
                serialNumber: '200900080',
                assetTag: 'M-A01-L2-001',
                location: {
                  zone: 'Zone A',
                  station: 'Pump Station A01',
                  line: 'Line 2',
                  position: 'M2',
                  coordinates: { x: 122, y: 111 }
                },
                specifications: realEquipmentSpecs.eldinMotor,
                status: 'operational',
                condition: 'fair',
                installationDate: '2023-01-15',
                operatingHours: 8500,
                tags: ['critical', 'main-motor', 'line-2']
              }
            ]
          },
          // Line 3
          {
            id: 'LINE-A01-L3',
            name: 'Line 3',
            type: 'line',
            parentId: 'STATION-A01',
            path: 'Zone A/Pump Station A01/Line 3',
            level: 2,
            location: {
              zone: 'Zone A',
              station: 'Pump Station A01',
              line: 'Line 3',
              coordinates: { x: 130, y: 110 }
            },
            assetCount: 0,
            children: [
              {
                id: 'P-A01-L3-001',
                name: 'Pump P3',
                type: 'equipment',
                parentId: 'LINE-A01-L3',
                path: 'Zone A/Pump Station A01/Line 3/Pump P3',
                level: 3,
                equipmentType: 'mechanical',
                category: 'pump',
                manufacturer: 'HMS',
                model: 'D200-500A-GGG(485)',
                serialNumber: '9û8',
                assetTag: 'P-A01-L3-001',
                location: {
                  zone: 'Zone A',
                  station: 'Pump Station A01',
                  line: 'Line 3',
                  position: 'P3',
                  coordinates: { x: 131, y: 111 }
                },
                specifications: realEquipmentSpecs.hmsPump,
                status: 'maintenance',
                condition: 'poor',
                installationDate: '2023-01-15',
                operatingHours: 9200,
                tags: ['critical', 'main-pump', 'line-3', 'maintenance-required']
              },
              {
                id: 'M-A01-L3-001',
                name: 'Motor M3',
                type: 'equipment',
                parentId: 'LINE-A01-L3',
                path: 'Zone A/Pump Station A01/Line 3/Motor M3',
                level: 3,
                equipmentType: 'electrical',
                category: 'motor',
                manufacturer: 'ELDIN',
                model: 'А355SMA4FБT2',
                serialNumber: '200900081',
                assetTag: 'M-A01-L3-001',
                location: {
                  zone: 'Zone A',
                  station: 'Pump Station A01',
                  line: 'Line 3',
                  position: 'M3',
                  coordinates: { x: 132, y: 111 }
                },
                specifications: realEquipmentSpecs.eldinMotor,
                status: 'maintenance',
                condition: 'poor',
                installationDate: '2023-01-15',
                operatingHours: 9200,
                tags: ['critical', 'main-motor', 'line-3', 'maintenance-required']
              }
            ]
          },
          // Line 4
          {
            id: 'LINE-A01-L4',
            name: 'Line 4',
            type: 'line',
            parentId: 'STATION-A01',
            path: 'Zone A/Pump Station A01/Line 4',
            level: 2,
            location: {
              zone: 'Zone A',
              station: 'Pump Station A01',
              line: 'Line 4',
              coordinates: { x: 140, y: 110 }
            },
            assetCount: 0,
            children: [
              {
                id: 'P-A01-L4-001',
                name: 'Pump P4',
                type: 'equipment',
                parentId: 'LINE-A01-L4',
                path: 'Zone A/Pump Station A01/Line 4/Pump P4',
                level: 3,
                equipmentType: 'mechanical',
                category: 'pump',
                manufacturer: 'HMS',
                model: 'D200-500A-GGG(485)',
                serialNumber: '9û9',
                assetTag: 'P-A01-L4-001',
                location: {
                  zone: 'Zone A',
                  station: 'Pump Station A01',
                  line: 'Line 4',
                  position: 'P4',
                  coordinates: { x: 141, y: 111 }
                },
                specifications: realEquipmentSpecs.hmsPump,
                status: 'standby',
                condition: 'excellent',
                installationDate: '2023-02-01',
                operatingHours: 2000,
                tags: ['standby', 'main-pump', 'line-4']
              },
              {
                id: 'M-A01-L4-001',
                name: 'Motor M4',
                type: 'equipment',
                parentId: 'LINE-A01-L4',
                path: 'Zone A/Pump Station A01/Line 4/Motor M4',
                level: 3,
                equipmentType: 'electrical',
                category: 'motor',
                manufacturer: 'ELDIN',
                model: 'А355SMA4FБT2',
                serialNumber: '200900082',
                assetTag: 'M-A01-L4-001',
                location: {
                  zone: 'Zone A',
                  station: 'Pump Station A01',
                  line: 'Line 4',
                  position: 'M4',
                  coordinates: { x: 142, y: 111 }
                },
                specifications: realEquipmentSpecs.eldinMotor,
                status: 'standby',
                condition: 'excellent',
                installationDate: '2023-02-01',
                operatingHours: 2000,
                tags: ['standby', 'main-motor', 'line-4']
              }
            ]
          },
          // Priming System
          {
            id: 'SYS-A01-PS',
            name: 'Priming System',
            type: 'system',
            parentId: 'STATION-A01',
            path: 'Zone A/Pump Station A01/Priming System',
            level: 2,
            location: {
              zone: 'Zone A',
              station: 'Pump Station A01',
              system: 'Priming System',
              coordinates: { x: 120, y: 120 }
            },
            assetCount: 0,
            children: [
              {
                id: 'PP-A01-PS-001',
                name: 'Priming Pump PP1',
                type: 'equipment',
                parentId: 'SYS-A01-PS',
                path: 'Zone A/Pump Station A01/Priming System/Priming Pump PP1',
                level: 3,
                equipmentType: 'mechanical',
                category: 'pump',
                manufacturer: 'ROBUSCHI',
                model: 'RVS14/M',
                serialNumber: 'RVS-001',
                assetTag: 'PP-A01-PS-001',
                location: {
                  zone: 'Zone A',
                  station: 'Pump Station A01',
                  system: 'Priming System',
                  position: 'PP1',
                  coordinates: { x: 121, y: 121 }
                },
                specifications: realEquipmentSpecs.robuschiPump,
                status: 'operational',
                condition: 'good',
                installationDate: '2023-01-20',
                operatingHours: 2000,
                tags: ['priming', 'vacuum', 'auxiliary']
              },
              {
                id: 'C-A01-PS-001',
                name: 'Compressor C1',
                type: 'equipment',
                parentId: 'SYS-A01-PS',
                path: 'Zone A/Pump Station A01/Priming System/Compressor C1',
                level: 3,
                equipmentType: 'mechanical',
                category: 'compressor',
                manufacturer: 'El Haggar Misr',
                model: 'HGF 500/580',
                serialNumber: 'EHM-001',
                assetTag: 'C-A01-PS-001',
                location: {
                  zone: 'Zone A',
                  station: 'Pump Station A01',
                  system: 'Priming System',
                  position: 'C1',
                  coordinates: { x: 122, y: 121 }
                },
                specifications: realEquipmentSpecs.elHaggarCompressor,
                status: 'operational',
                condition: 'good',
                installationDate: '2023-01-20',
                operatingHours: 1500,
                tags: ['compressor', 'air', 'auxiliary']
              }
            ]
          },
          // Water Hammer System
          {
            id: 'SYS-A01-WH',
            name: 'Water Hammer System',
            type: 'system',
            parentId: 'STATION-A01',
            path: 'Zone A/Pump Station A01/Water Hammer System',
            level: 2,
            location: {
              zone: 'Zone A',
              station: 'Pump Station A01',
              system: 'Water Hammer System',
              coordinates: { x: 130, y: 130 }
            },
            assetCount: 0,
            children: [
              {
                id: 'T-A01-WH-001',
                name: 'Water Hammer Tank T1',
                type: 'equipment',
                parentId: 'SYS-A01-WH',
                path: 'Zone A/Pump Station A01/Water Hammer System/Water Hammer Tank T1',
                level: 3,
                equipmentType: 'mechanical',
                category: 'tank',
                manufacturer: 'HC',
                model: 'WHT-35000',
                serialNumber: 'HC-T1-001',
                assetTag: 'T-A01-WH-001',
                location: {
                  zone: 'Zone A',
                  station: 'Pump Station A01',
                  system: 'Water Hammer System',
                  position: 'T1',
                  coordinates: { x: 131, y: 131 }
                },
                specifications: realEquipmentSpecs.hcTank,
                status: 'operational',
                condition: 'good',
                installationDate: '2023-01-10',
                operatingHours: 8760,
                tags: ['tank', 'water-hammer', 'protection']
              },
              {
                id: 'CV-A01-WH-001',
                name: 'Check Valve CV1',
                type: 'equipment',
                parentId: 'SYS-A01-WH',
                path: 'Zone A/Pump Station A01/Water Hammer System/Check Valve CV1',
                level: 3,
                equipmentType: 'mechanical',
                category: 'valve',
                manufacturer: 'Generic',
                model: 'CV-150',
                serialNumber: 'V-A01-WH-001',
                assetTag: 'CV-A01-WH-001',
                location: {
                  zone: 'Zone A',
                  station: 'Pump Station A01',
                  system: 'Water Hammer System',
                  position: 'CV1',
                  coordinates: { x: 132, y: 131 }
                },
                specifications: realEquipmentSpecs.checkValve,
                status: 'operational',
                condition: 'good',
                installationDate: '2023-01-10',
                operatingHours: 8760,
                tags: ['valve', 'check', 'protection']
              }
            ]
          }
        ]
      }
    ]
  }
];

// Asset categories for the hierarchical system
export const hierarchicalAssetCategories = [
  { value: "pump", label: "Pumps" },
  { value: "motor", label: "Motors" },
  { value: "valve", label: "Valves" },
  { value: "compressor", label: "Compressors" },
  { value: "tank", label: "Tanks" },
  { value: "sensor", label: "Sensors" },
  { value: "panel", label: "Panels" },
  { value: "transformer", label: "Transformers" }
];

// Hierarchy levels for filtering
export const hierarchyLevels = [
  { value: "zone", label: "Zones" },
  { value: "station", label: "Stations" },
  { value: "line", label: "Lines" },
  { value: "system", label: "Systems" },
  { value: "equipment", label: "Equipment" }
];

// Real manufacturers from the equipment data
export const realManufacturers = [
  "HMS",
  "ELDIN",
  "ROBUSCHI",
  "El Haggar Misr",
  "HC",
  "Generic"
];

export default hierarchicalAssetData;
