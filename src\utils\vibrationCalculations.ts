/**
 * Advanced Vibration Calculation Utilities
 * Centralized calculations for ISO 10816, RMS velocity, Weibull analysis, and more
 */

export interface VibrationMeasurement {
  velV: number;
  velH: number;
  velAxl: number;
  accV?: number;
  accH?: number;
  accAxl?: number;
  temp?: number;
  displacement?: number;
}

export interface ISO10816Zone {
  zone: 'A' | 'B' | 'C' | 'D';
  max: number;
  label: string;
  healthScore: number;
  color: string;
  recommendation: string;
}

export interface WeibullParameters {
  shapeParameter: number; // β (beta)
  scaleParameter: number; // η (eta)
  locationParameter: number; // γ (gamma)
  reliability: (t: number) => number;
  hazardRate: (t: number) => number;
  meanTimeToFailure: number;
  confidenceInterval: {
    lower: number;
    upper: number;
  };
}

export interface VibrationAnalysis {
  rmsVelocity: number;
  peakVelocity: number;
  creastFactor: number;
  kurtosis: number;
  skewness: number;
  iso10816Zone: ISO10816Zone;
  frequencyDomainAnalysis: {
    fundamentalFrequency: number;
    harmonics: number[];
    sideband: number[];
    bearingFrequencies: number[];
  };
  timeSeriesAnalysis: {
    trend: 'improving' | 'stable' | 'degrading';
    seasonality: boolean;
    anomalies: number[];
  };
}

// ISO 10816 Standard Thresholds
export const ISO10816_ZONES: ISO10816Zone[] = [
  {
    zone: 'A',
    max: 1.8,
    label: 'Good',
    healthScore: 95,
    color: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    recommendation: 'Continue normal operation'
  },
  {
    zone: 'B',
    max: 4.5,
    label: 'Satisfactory',
    healthScore: 80,
    color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
    recommendation: 'Monitor closely, schedule routine inspection'
  },
  {
    zone: 'C',
    max: 7.1,
    label: 'Unsatisfactory',
    healthScore: 60,
    color: 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400',
    recommendation: 'Schedule maintenance within 30 days'
  },
  {
    zone: 'D',
    max: Infinity,
    label: 'Unacceptable',
    healthScore: 30,
    color: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
    recommendation: 'URGENT: Stop operation and inspect immediately'
  }
];

/**
 * Calculate RMS velocity from velocity components
 */
export function calculateRMSVelocity(measurement: VibrationMeasurement): number {
  const velocities = [measurement.velV, measurement.velH, measurement.velAxl]
    .filter(v => v !== undefined && !isNaN(v) && v !== null);
  
  if (velocities.length === 0) return 0;
  
  const sumOfSquares = velocities.reduce((sum, v) => sum + v * v, 0);
  return Math.sqrt(sumOfSquares / velocities.length);
}

/**
 * Calculate peak velocity from velocity components
 */
export function calculatePeakVelocity(measurement: VibrationMeasurement): number {
  const velocities = [measurement.velV, measurement.velH, measurement.velAxl]
    .filter(v => v !== undefined && !isNaN(v) && v !== null);
  
  return Math.max(...velocities, 0);
}

/**
 * Determine ISO 10816 zone based on RMS velocity
 */
export function getISO10816Zone(rmsVelocity: number): ISO10816Zone {
  return ISO10816_ZONES.find(zone => rmsVelocity <= zone.max) || ISO10816_ZONES[ISO10816_ZONES.length - 1];
}

/**
 * Calculate crest factor (peak/RMS ratio)
 */
export function calculateCrestFactor(measurement: VibrationMeasurement): number {
  const rms = calculateRMSVelocity(measurement);
  const peak = calculatePeakVelocity(measurement);
  
  return rms > 0 ? peak / rms : 0;
}

/**
 * Calculate kurtosis (measure of tail heaviness)
 */
export function calculateKurtosis(values: number[]): number {
  if (values.length < 4) return 0;
  
  const mean = values.reduce((sum, v) => sum + v, 0) / values.length;
  const variance = values.reduce((sum, v) => sum + Math.pow(v - mean, 2), 0) / values.length;
  const fourthMoment = values.reduce((sum, v) => sum + Math.pow(v - mean, 4), 0) / values.length;
  
  return variance > 0 ? fourthMoment / Math.pow(variance, 2) - 3 : 0;
}

/**
 * Calculate skewness (measure of asymmetry)
 */
export function calculateSkewness(values: number[]): number {
  if (values.length < 3) return 0;
  
  const mean = values.reduce((sum, v) => sum + v, 0) / values.length;
  const variance = values.reduce((sum, v) => sum + Math.pow(v - mean, 2), 0) / values.length;
  const thirdMoment = values.reduce((sum, v) => sum + Math.pow(v - mean, 3), 0) / values.length;
  
  return variance > 0 ? thirdMoment / Math.pow(variance, 1.5) : 0;
}

/**
 * Perform comprehensive vibration analysis
 */
export function performVibrationAnalysis(
  measurement: VibrationMeasurement,
  historicalData: VibrationMeasurement[] = []
): VibrationAnalysis {
  const rmsVelocity = calculateRMSVelocity(measurement);
  const peakVelocity = calculatePeakVelocity(measurement);
  const creastFactor = calculateCrestFactor(measurement);
  const iso10816Zone = getISO10816Zone(rmsVelocity);
  
  // Historical analysis
  const historicalRMS = historicalData.map(m => calculateRMSVelocity(m));
  const kurtosis = calculateKurtosis(historicalRMS);
  const skewness = calculateSkewness(historicalRMS);
  
  // Trend analysis
  let trend: 'improving' | 'stable' | 'degrading' = 'stable';
  if (historicalRMS.length >= 3) {
    const recent = historicalRMS.slice(-3);
    const slope = (recent[2] - recent[0]) / 2;
    trend = slope > 0.5 ? 'degrading' : slope < -0.5 ? 'improving' : 'stable';
  }
  
  // Frequency domain analysis (simplified)
  const fundamentalFrequency = 50; // Hz (typical for 50Hz power systems)
  const harmonics = [100, 150, 200, 250]; // 2x, 3x, 4x, 5x harmonics
  const bearingFrequencies = [
    fundamentalFrequency * 0.4, // Ball pass frequency outer race
    fundamentalFrequency * 0.6, // Ball pass frequency inner race
    fundamentalFrequency * 0.15, // Cage frequency
    fundamentalFrequency * 2.5   // Ball spin frequency
  ];
  
  return {
    rmsVelocity,
    peakVelocity,
    creastFactor,
    kurtosis,
    skewness,
    iso10816Zone,
    frequencyDomainAnalysis: {
      fundamentalFrequency,
      harmonics,
      sideband: [fundamentalFrequency - 5, fundamentalFrequency + 5],
      bearingFrequencies
    },
    timeSeriesAnalysis: {
      trend,
      seasonality: false, // Would require more sophisticated analysis
      anomalies: [] // Would require anomaly detection algorithm
    }
  };
}

/**
 * Calculate Weibull parameters from failure data
 */
export function calculateWeibullParameters(
  failureTimes: number[],
  operatingHours: number[]
): WeibullParameters {
  // Simplified Weibull calculation - in production would use maximum likelihood estimation
  if (failureTimes.length < 2) {
    return {
      shapeParameter: 2.0,
      scaleParameter: 8760,
      locationParameter: 0,
      reliability: (t: number) => Math.exp(-Math.pow(t / 8760, 2.0)),
      hazardRate: (t: number) => (2.0 / 8760) * Math.pow(t / 8760, 1.0),
      meanTimeToFailure: 8760 * Math.gamma(1 + 1/2.0),
      confidenceInterval: { lower: 1.5, upper: 2.5 }
    };
  }
  
  // Estimate shape parameter (β) using method of moments
  const meanFailureTime = failureTimes.reduce((sum, t) => sum + t, 0) / failureTimes.length;
  const variance = failureTimes.reduce((sum, t) => sum + Math.pow(t - meanFailureTime, 2), 0) / failureTimes.length;
  const coefficientOfVariation = Math.sqrt(variance) / meanFailureTime;
  
  // Approximate shape parameter from coefficient of variation
  const shapeParameter = Math.max(0.5, Math.min(5.0, 1 / coefficientOfVariation));
  
  // Estimate scale parameter (η)
  const scaleParameter = meanFailureTime / Math.gamma(1 + 1/shapeParameter);
  
  return {
    shapeParameter,
    scaleParameter,
    locationParameter: 0,
    reliability: (t: number) => Math.exp(-Math.pow(t / scaleParameter, shapeParameter)),
    hazardRate: (t: number) => (shapeParameter / scaleParameter) * Math.pow(t / scaleParameter, shapeParameter - 1),
    meanTimeToFailure: scaleParameter * Math.gamma(1 + 1/shapeParameter),
    confidenceInterval: {
      lower: shapeParameter * 0.8,
      upper: shapeParameter * 1.2
    }
  };
}

/**
 * Calculate equipment health score based on multiple factors
 */
export function calculateHealthScore(
  vibrationAnalysis: VibrationAnalysis,
  temperature: number = 25,
  operatingHours: number = 0,
  lastMaintenanceHours: number = 0
): number {
  let healthScore = vibrationAnalysis.iso10816Zone.healthScore;
  
  // Temperature penalty
  if (temperature > 80) healthScore -= 10;
  else if (temperature > 60) healthScore -= 5;
  
  // Operating hours penalty
  const hoursPercentage = operatingHours / 8760; // Percentage of year
  if (hoursPercentage > 0.9) healthScore -= 5;
  
  // Maintenance overdue penalty
  const hoursSinceMaintenance = operatingHours - lastMaintenanceHours;
  if (hoursSinceMaintenance > 4380) healthScore -= 10; // 6 months
  else if (hoursSinceMaintenance > 2190) healthScore -= 5; // 3 months
  
  // Trend penalty
  if (vibrationAnalysis.timeSeriesAnalysis.trend === 'degrading') healthScore -= 10;
  else if (vibrationAnalysis.timeSeriesAnalysis.trend === 'improving') healthScore += 5;
  
  // Crest factor penalty (high crest factor indicates impacting)
  if (vibrationAnalysis.creastFactor > 5) healthScore -= 15;
  else if (vibrationAnalysis.creastFactor > 3) healthScore -= 5;
  
  return Math.max(0, Math.min(100, healthScore));
}

/**
 * Generate maintenance recommendations based on analysis
 */
export function generateMaintenanceRecommendations(
  vibrationAnalysis: VibrationAnalysis,
  healthScore: number
): string[] {
  const recommendations: string[] = [];
  
  // Zone-based recommendations
  recommendations.push(vibrationAnalysis.iso10816Zone.recommendation);
  
  // Trend-based recommendations
  if (vibrationAnalysis.timeSeriesAnalysis.trend === 'degrading') {
    recommendations.push('Investigate cause of increasing vibration levels');
    recommendations.push('Consider advanced diagnostics (FFT analysis, envelope detection)');
  }
  
  // Crest factor recommendations
  if (vibrationAnalysis.creastFactor > 5) {
    recommendations.push('High crest factor detected - check for bearing defects or looseness');
  }
  
  // Health score recommendations
  if (healthScore < 60) {
    recommendations.push('Schedule immediate inspection');
    recommendations.push('Prepare for potential equipment shutdown');
  } else if (healthScore < 80) {
    recommendations.push('Increase monitoring frequency');
    recommendations.push('Plan maintenance within next scheduled window');
  }
  
  return recommendations;
}

/**
 * Calculate cost impact based on vibration condition
 */
export function calculateCostImpact(
  vibrationAnalysis: VibrationAnalysis,
  equipmentValue: number = 100000
): {
  immediateCost: number;
  projectedAnnualCost: number;
  riskMultiplier: number;
} {
  const zone = vibrationAnalysis.iso10816Zone.zone;
  
  const riskMultiplier = zone === 'A' ? 1 : zone === 'B' ? 1.5 : zone === 'C' ? 3 : 6;
  const baseCost = equipmentValue * 0.05; // 5% of equipment value as base maintenance cost
  
  return {
    immediateCost: zone === 'D' ? baseCost * 2 : zone === 'C' ? baseCost * 0.5 : 0,
    projectedAnnualCost: baseCost * riskMultiplier,
    riskMultiplier
  };
}
