/**
 * Vibration Data Flow Dashboard
 * Real-time visualization of vibration data propagation across all EAMS modules
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Activity, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Zap, 
  Database, 
  TrendingUp,
  AlertTriangle,
  RefreshCw,
  Eye,
  BarChart3
} from 'lucide-react';
import { useVibrationData } from '@/contexts/VibrationDataContext';
import { vibrationDataFlowEngine } from '@/services/vibrationDataFlowEngine';
import { EnhancedChart } from '@/components/charts/EnhancedChart';

interface ModuleStatus {
  name: string;
  status: 'idle' | 'processing' | 'success' | 'error';
  lastUpdate: string;
  processingTime: number;
  icon: React.ReactNode;
  color: string;
}

export function VibrationDataFlowDashboard() {
  const { isProcessing, lastUpdateTimestamp } = useVibrationData();
  const [metrics, setMetrics] = useState(vibrationDataFlowEngine.getMetrics());
  const [eventQueue, setEventQueue] = useState(vibrationDataFlowEngine.getEventQueue());
  const [moduleStatuses, setModuleStatuses] = useState<ModuleStatus[]>([
    {
      name: 'Asset Management',
      status: 'idle',
      lastUpdate: 'Never',
      processingTime: 0,
      icon: <Database className="h-4 w-4" />,
      color: 'bg-blue-500'
    },
    {
      name: 'Condition Monitoring',
      status: 'idle',
      lastUpdate: 'Never',
      processingTime: 0,
      icon: <Activity className="h-4 w-4" />,
      color: 'bg-green-500'
    },
    {
      name: 'Maintenance',
      status: 'idle',
      lastUpdate: 'Never',
      processingTime: 0,
      icon: <AlertTriangle className="h-4 w-4" />,
      color: 'bg-orange-500'
    },
    {
      name: 'Financial',
      status: 'idle',
      lastUpdate: 'Never',
      processingTime: 0,
      icon: <TrendingUp className="h-4 w-4" />,
      color: 'bg-purple-500'
    },
    {
      name: 'Analytics',
      status: 'idle',
      lastUpdate: 'Never',
      processingTime: 0,
      icon: <BarChart3 className="h-4 w-4" />,
      color: 'bg-cyan-500'
    },
    {
      name: 'ML Pipeline',
      status: 'idle',
      lastUpdate: 'Never',
      processingTime: 0,
      icon: <Zap className="h-4 w-4" />,
      color: 'bg-yellow-500'
    }
  ]);

  // Update metrics and event queue periodically
  useEffect(() => {
    const interval = setInterval(() => {
      setMetrics(vibrationDataFlowEngine.getMetrics());
      setEventQueue(vibrationDataFlowEngine.getEventQueue());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Update module statuses based on processing state
  useEffect(() => {
    if (isProcessing) {
      setModuleStatuses(prev => 
        prev.map(module => ({
          ...module,
          status: 'processing' as const
        }))
      );
    } else if (lastUpdateTimestamp) {
      setModuleStatuses(prev => 
        prev.map(module => ({
          ...module,
          status: 'success' as const,
          lastUpdate: new Date(lastUpdateTimestamp).toLocaleTimeString(),
          processingTime: Math.random() * 500 + 100 // Simulated processing time
        }))
      );
    }
  }, [isProcessing, lastUpdateTimestamp]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'processing':
        return <RefreshCw className="h-4 w-4 animate-spin" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'processing':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'success':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'error':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  // Chart data for metrics visualization
  const metricsChartData = {
    labels: ['Successful', 'Failed'],
    datasets: [{
      label: 'Propagations',
      data: [metrics.successfulPropagations, metrics.failedPropagations],
      backgroundColor: ['#10b981', '#ef4444'],
      borderColor: ['#059669', '#dc2626'],
      borderWidth: 2
    }]
  };

  const processingTimeChartData = {
    labels: moduleStatuses.map(m => m.name),
    datasets: [{
      label: 'Processing Time (ms)',
      data: moduleStatuses.map(m => m.processingTime),
      backgroundColor: moduleStatuses.map(m => m.color),
      borderColor: moduleStatuses.map(m => m.color.replace('bg-', 'border-')),
      borderWidth: 2
    }]
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Vibration Data Flow Dashboard</h2>
          <p className="text-muted-foreground">
            Real-time monitoring of vibration data propagation across all EAMS modules
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={isProcessing ? "default" : "secondary"} className="flex items-center gap-1">
            {isProcessing ? (
              <>
                <RefreshCw className="h-3 w-3 animate-spin" />
                Processing
              </>
            ) : (
              <>
                <CheckCircle className="h-3 w-3" />
                Ready
              </>
            )}
          </Badge>
        </div>
      </div>

      {/* Overview Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Events</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalEvents}</div>
            <p className="text-xs text-muted-foreground">
              Data flow events processed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics.totalEvents > 0 
                ? Math.round((metrics.successfulPropagations / (metrics.successfulPropagations + metrics.failedPropagations)) * 100)
                : 100}%
            </div>
            <p className="text-xs text-muted-foreground">
              Successful propagations
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Processing Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Math.round(metrics.averageProcessingTime)}ms</div>
            <p className="text-xs text-muted-foreground">
              Per vibration reading
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Queue Size</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{eventQueue.length}</div>
            <p className="text-xs text-muted-foreground">
              Events in queue
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="modules" className="space-y-4">
        <TabsList>
          <TabsTrigger value="modules">Module Status</TabsTrigger>
          <TabsTrigger value="events">Event Queue</TabsTrigger>
          <TabsTrigger value="metrics">Performance Metrics</TabsTrigger>
        </TabsList>

        <TabsContent value="modules" className="space-y-4">
          {/* Module Status Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {moduleStatuses.map((module) => (
              <Card key={module.name} className="relative overflow-hidden">
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {module.icon}
                      <CardTitle className="text-sm">{module.name}</CardTitle>
                    </div>
                    {getStatusIcon(module.status)}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Badge className={getStatusColor(module.status)}>
                      {module.status.charAt(0).toUpperCase() + module.status.slice(1)}
                    </Badge>
                    <div className="text-xs text-muted-foreground">
                      Last Update: {module.lastUpdate}
                    </div>
                    {module.processingTime > 0 && (
                      <div className="text-xs text-muted-foreground">
                        Processing Time: {Math.round(module.processingTime)}ms
                      </div>
                    )}
                    {module.status === 'processing' && (
                      <Progress value={Math.random() * 100} className="h-1" />
                    )}
                  </div>
                </CardContent>
                <div 
                  className={`absolute top-0 left-0 w-1 h-full ${module.color}`}
                />
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="events" className="space-y-4">
          {/* Event Queue */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Events</CardTitle>
              <CardDescription>
                Latest vibration data flow events and their propagation status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {eventQueue.slice(-10).reverse().map((event) => (
                  <div key={event.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(event.status)}
                      <div>
                        <div className="font-medium text-sm">{event.type.replace('_', ' ').toUpperCase()}</div>
                        <div className="text-xs text-muted-foreground">
                          Equipment: {event.equipmentId} • {new Date(event.timestamp).toLocaleTimeString()}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge className={getStatusColor(event.status)}>
                        {event.status}
                      </Badge>
                      <div className="text-xs text-muted-foreground mt-1">
                        Modules: {event.propagatedTo.length}
                      </div>
                    </div>
                  </div>
                ))}
                {eventQueue.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    No events in queue
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="metrics" className="space-y-4">
          {/* Performance Metrics Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Propagation Success Rate</CardTitle>
                <CardDescription>
                  Success vs failure rate for data propagation
                </CardDescription>
              </CardHeader>
              <CardContent>
                <EnhancedChart
                  title=""
                  type="doughnut"
                  data={metricsChartData}
                  height={300}
                  showControls={false}
                  showLegend={true}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Module Processing Times</CardTitle>
                <CardDescription>
                  Average processing time per module
                </CardDescription>
              </CardHeader>
              <CardContent>
                <EnhancedChart
                  title=""
                  type="bar"
                  data={processingTimeChartData}
                  height={300}
                  showControls={false}
                  showLegend={false}
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
