import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { Equipment } from '@/types/eams';
import { VibrationHistoryRecord } from '@/data/vibrationHistoryData';
import { useAssetContext } from './AssetContext';
import { useNotifications } from '@/components/ui/notification-system';
import { saveToLocalStorage, loadFromLocalStorage } from '@/utils/localStorageUtils';

// Enhanced vibration calculation utilities
export interface VibrationCalculations {
  rmsVelocity: number;
  iso10816Zone: 'A' | 'B' | 'C' | 'D';
  healthScore: number;
  trendDirection: 'improving' | 'stable' | 'degrading';
  alertLevel: 'none' | 'warning' | 'critical' | 'emergency';
}

export interface VibrationDataPoint {
  equipmentId: string;
  timestamp: string;
  pumpNDE: VibrationCalculations;
  pumpDE: VibrationCalculations;
  motorNDE: VibrationCalculations;
  motorDE: VibrationCalculations;
  overallHealth: number;
  overallZone: 'A' | 'B' | 'C' | 'D';
  maintenanceRecommendation: string;
  costImpact: number;
}

export interface VibrationDataContextType {
  // Core data management
  vibrationData: Map<string, VibrationDataPoint[]>;
  addVibrationReading: (record: VibrationHistoryRecord) => Promise<void>;
  getLatestVibrationData: (equipmentId: string) => VibrationDataPoint | null;
  getVibrationTrend: (equipmentId: string, days: number) => VibrationDataPoint[];

  // Real-time calculations
  calculateVibrationMetrics: (rawData: any) => VibrationCalculations;
  calculateOverallHealth: (equipmentId: string) => number;

  // Cross-module integration
  propagateToAssetManagement: (equipmentId: string, data: VibrationDataPoint) => void;
  propagateToMaintenance: (equipmentId: string, data: VibrationDataPoint) => void;
  propagateToFinancial: (equipmentId: string, data: VibrationDataPoint) => void;
  propagateToAnalytics: (equipmentId: string, data: VibrationDataPoint) => void;
  propagateToMLPipeline: (equipmentId: string, data: VibrationDataPoint) => void;

  // Event system
  subscribeToVibrationUpdates: (equipmentId: string, callback: (data: VibrationDataPoint) => void) => () => void;
  triggerVibrationAlert: (equipmentId: string, alertLevel: string, message: string) => void;

  // Performance optimization
  isProcessing: boolean;
  lastUpdateTimestamp: number;
  batchUpdates: (updates: VibrationHistoryRecord[]) => Promise<void>;
}

const VibrationDataContext = createContext<VibrationDataContextType | undefined>(undefined);

// ISO 10816 thresholds - centralized configuration
const ISO10816_THRESHOLDS = [
  { zone: 'A' as const, max: 1.8, label: 'Good', healthScore: 95 },
  { zone: 'B' as const, max: 4.5, label: 'Satisfactory', healthScore: 80 },
  { zone: 'C' as const, max: 7.1, label: 'Unsatisfactory', healthScore: 60 },
  { zone: 'D' as const, max: Infinity, label: 'Unacceptable', healthScore: 30 },
];

// Helper function to calculate next inspection date
const calculateNextInspection = (zone: 'A' | 'B' | 'C' | 'D'): string => {
  const now = new Date();
  const daysToAdd = zone === 'A' ? 90 : zone === 'B' ? 60 : zone === 'C' ? 30 : 7;
  now.setDate(now.getDate() + daysToAdd);
  return now.toISOString().split('T')[0];
};

export const VibrationDataProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { equipment, setEquipment, triggerDataUpdate } = useAssetContext();
  const { addNotification } = useNotifications();

  // Core state
  const [vibrationData, setVibrationData] = useState<Map<string, VibrationDataPoint[]>>(new Map());
  const [isProcessing, setIsProcessing] = useState(false);
  const [lastUpdateTimestamp, setLastUpdateTimestamp] = useState(Date.now());

  // Event subscribers
  const subscribersRef = useRef<Map<string, Set<(data: VibrationDataPoint) => void>>>(new Map());

  // Centralized RMS calculation
  const calculateRMSVelocity = useCallback((values: any): number => {
    const v = [values.velV, values.velH, values.velAxl].map(Number).filter(x => !isNaN(x));
    if (v.length === 0) return 0;
    return Math.sqrt(v.reduce((sum: number, x: number) => sum + x * x, 0) / v.length);
  }, []);

  // Centralized ISO 10816 zone calculation
  const getISO10816Zone = useCallback((rms: number) => {
    return ISO10816_THRESHOLDS.find(z => rms <= z.max) || ISO10816_THRESHOLDS[ISO10816_THRESHOLDS.length - 1];
  }, []);

  // Enhanced vibration metrics calculation
  const calculateVibrationMetrics = useCallback((rawData: any): VibrationCalculations => {
    const rmsVelocity = calculateRMSVelocity(rawData);
    const zone = getISO10816Zone(rmsVelocity);

    // Calculate health score based on multiple factors
    const baseHealthScore = zone.healthScore;
    const temperaturePenalty = rawData.temp > 80 ? 10 : 0;
    const healthScore = Math.max(0, baseHealthScore - temperaturePenalty);

    // Determine trend direction (simplified - would use historical data in production)
    const trendDirection: 'improving' | 'stable' | 'degrading' =
      rmsVelocity < 2.0 ? 'improving' : rmsVelocity > 6.0 ? 'degrading' : 'stable';

    // Determine alert level
    const alertLevel: 'none' | 'warning' | 'critical' | 'emergency' =
      zone.zone === 'A' ? 'none' :
        zone.zone === 'B' ? 'warning' :
          zone.zone === 'C' ? 'critical' : 'emergency';

    return {
      rmsVelocity,
      iso10816Zone: zone.zone,
      healthScore,
      trendDirection,
      alertLevel
    };
  }, [calculateRMSVelocity, getISO10816Zone]);

  // Calculate overall equipment health
  const calculateOverallHealth = useCallback((equipmentId: string): number => {
    const latestData = getLatestVibrationData(equipmentId);
    if (!latestData) return 100;

    const scores = [
      latestData.pumpNDE.healthScore,
      latestData.pumpDE.healthScore,
      latestData.motorNDE.healthScore,
      latestData.motorDE.healthScore
    ];

    return scores.reduce((sum, score) => sum + score, 0) / scores.length;
  }, []);

  // Get latest vibration data for equipment
  const getLatestVibrationData = useCallback((equipmentId: string): VibrationDataPoint | null => {
    const equipmentData = vibrationData.get(equipmentId);
    if (!equipmentData || equipmentData.length === 0) return null;
    return equipmentData[equipmentData.length - 1];
  }, [vibrationData]);

  // Get vibration trend data
  const getVibrationTrend = useCallback((equipmentId: string, days: number): VibrationDataPoint[] => {
    const equipmentData = vibrationData.get(equipmentId) || [];
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    return equipmentData.filter(point => new Date(point.timestamp) >= cutoffDate);
  }, [vibrationData]);

  // Core vibration data processing
  const addVibrationReading = useCallback(async (record: VibrationHistoryRecord): Promise<void> => {
    setIsProcessing(true);

    try {
      // Calculate metrics for all measurement points
      const pumpNDE = calculateVibrationMetrics(record.pumpData?.nde || {});
      const pumpDE = calculateVibrationMetrics(record.pumpData?.de || {});
      const motorNDE = calculateVibrationMetrics(record.motorData?.nde || {});
      const motorDE = calculateVibrationMetrics(record.motorData?.de || {});

      // Calculate overall metrics
      const overallHealth = [pumpNDE.healthScore, pumpDE.healthScore, motorNDE.healthScore, motorDE.healthScore]
        .reduce((sum, score) => sum + score, 0) / 4;

      const worstZone = [pumpNDE.iso10816Zone, pumpDE.iso10816Zone, motorNDE.iso10816Zone, motorDE.iso10816Zone]
        .reduce((worst, current) => {
          const worstIndex = ISO10816_THRESHOLDS.findIndex(z => z.zone === worst);
          const currentIndex = ISO10816_THRESHOLDS.findIndex(z => z.zone === current);
          return currentIndex > worstIndex ? current : worst;
        }, 'A' as const);

      // Generate maintenance recommendation
      const maintenanceRecommendation = worstZone === 'A' ? 'Continue normal operation' :
        worstZone === 'B' ? 'Monitor closely, schedule inspection' :
          worstZone === 'C' ? 'Schedule maintenance within 30 days' :
            'URGENT: Stop operation and inspect immediately';

      // Calculate cost impact
      const costImpact = worstZone === 'A' ? 0 :
        worstZone === 'B' ? 5000 :
          worstZone === 'C' ? 15000 : 50000;

      const vibrationPoint: VibrationDataPoint = {
        equipmentId: record.equipmentId,
        timestamp: record.date,
        pumpNDE,
        pumpDE,
        motorNDE,
        motorDE,
        overallHealth,
        overallZone: worstZone,
        maintenanceRecommendation,
        costImpact
      };

      // Update vibration data
      setVibrationData(prev => {
        const newMap = new Map(prev);
        const equipmentData = newMap.get(record.equipmentId) || [];
        equipmentData.push(vibrationPoint);
        newMap.set(record.equipmentId, equipmentData);
        return newMap;
      });

      // Propagate to all modules
      await Promise.all([
        propagateToAssetManagement(record.equipmentId, vibrationPoint),
        propagateToMaintenance(record.equipmentId, vibrationPoint),
        propagateToFinancial(record.equipmentId, vibrationPoint),
        propagateToAnalytics(record.equipmentId, vibrationPoint),
        propagateToMLPipeline(record.equipmentId, vibrationPoint)
      ]);

      // Notify subscribers
      const subscribers = subscribersRef.current.get(record.equipmentId);
      if (subscribers) {
        subscribers.forEach(callback => callback(vibrationPoint));
      }

      // Trigger alerts if necessary
      if (vibrationPoint.overallZone === 'C' || vibrationPoint.overallZone === 'D') {
        triggerVibrationAlert(record.equipmentId, vibrationPoint.overallZone, maintenanceRecommendation);
      }

      setLastUpdateTimestamp(Date.now());

    } catch (error) {
      console.error('Error processing vibration reading:', error);
      addNotification({
        title: 'Vibration Data Error',
        message: 'Failed to process vibration reading',
        type: 'error'
      });
    } finally {
      setIsProcessing(false);
    }
  }, [calculateVibrationMetrics, addNotification]);

  // Cross-module propagation functions
  const propagateToAssetManagement = useCallback(async (equipmentId: string, data: VibrationDataPoint) => {
    setEquipment(prevEquipment =>
      prevEquipment.map(eq => {
        if (eq.id === equipmentId) {
          return {
            ...eq,
            conditionMonitoring: {
              ...eq.conditionMonitoring,
              vibration: {
                rmsVelocity: data.overallHealth / 10, // Convert health to RMS approximation
                peakVelocity: data.overallHealth / 8,
                zone: data.overallZone,
                lastUpdated: data.timestamp,
                trend: data.pumpNDE.trendDirection
              },
              healthScore: data.overallHealth,
              lastInspection: data.timestamp,
              nextInspection: calculateNextInspection(data.overallZone),
              alerts: data.overallZone === 'C' || data.overallZone === 'D' ?
                [`Zone ${data.overallZone}: ${data.maintenanceRecommendation}`] : []
            },
            status: data.overallZone === 'D' ? 'Critical' :
              data.overallZone === 'C' ? 'Warning' : 'Operational'
          };
        }
        return eq;
      })
    );
    triggerDataUpdate();
  }, [setEquipment, triggerDataUpdate]);

  const propagateToMaintenance = useCallback(async (equipmentId: string, data: VibrationDataPoint) => {
    // Auto-create work orders for critical conditions
    if (data.overallZone === 'C' || data.overallZone === 'D') {
      const workOrderData = {
        id: `WO-VIB-${equipmentId}-${Date.now()}`,
        equipmentId,
        title: `Vibration Alert - Zone ${data.overallZone}`,
        description: data.maintenanceRecommendation,
        priority: data.overallZone === 'D' ? 'Critical' : 'High',
        type: 'Corrective',
        status: 'Open',
        createdDate: new Date().toISOString(),
        estimatedCost: data.costImpact,
        vibrationData: data
      };

      // Store work order (would integrate with work order management system)
      saveToLocalStorage(`workOrder-${workOrderData.id}`, workOrderData);
    }
  }, []);

  const propagateToFinancial = useCallback(async (equipmentId: string, data: VibrationDataPoint) => {
    // Update maintenance cost projections
    const costProjection = {
      equipmentId,
      timestamp: data.timestamp,
      immediateMaintenanceCost: data.costImpact,
      projectedAnnualCost: data.costImpact * (data.overallZone === 'D' ? 4 : data.overallZone === 'C' ? 2 : 1),
      riskLevel: data.overallZone,
      healthScore: data.overallHealth
    };

    // Store financial impact data
    const existingProjections = loadFromLocalStorage('maintenanceCostProjections', []);
    existingProjections.push(costProjection);
    saveToLocalStorage('maintenanceCostProjections', existingProjections);
  }, []);

  const propagateToAnalytics = useCallback(async (equipmentId: string, data: VibrationDataPoint) => {
    // Update analytics dashboards
    const analyticsUpdate = {
      equipmentId,
      timestamp: data.timestamp,
      kpis: {
        overallEquipmentEffectiveness: Math.max(0, data.overallHealth - 10),
        meanTimeBetweenFailures: data.overallZone === 'A' ? 8760 : data.overallZone === 'B' ? 4380 : 2190,
        meanTimeToRepair: data.overallZone === 'A' ? 2 : data.overallZone === 'B' ? 4 : 8,
        availability: data.overallHealth / 100,
        reliability: data.overallHealth / 100
      },
      trends: {
        vibrationTrend: data.pumpNDE.trendDirection,
        healthTrend: data.overallHealth > 80 ? 'improving' : data.overallHealth < 60 ? 'degrading' : 'stable'
      }
    };

    // Store analytics data
    const existingAnalytics = loadFromLocalStorage('vibrationAnalytics', []);
    existingAnalytics.push(analyticsUpdate);
    saveToLocalStorage('vibrationAnalytics', existingAnalytics);
  }, []);

  const propagateToMLPipeline = useCallback(async (equipmentId: string, data: VibrationDataPoint) => {
    // Prepare data for ML pipeline
    const mlFeatures = {
      equipmentId,
      timestamp: data.timestamp,
      features: {
        pumpNDE_rms: data.pumpNDE.rmsVelocity,
        pumpDE_rms: data.pumpDE.rmsVelocity,
        motorNDE_rms: data.motorNDE.rmsVelocity,
        motorDE_rms: data.motorDE.rmsVelocity,
        overallHealth: data.overallHealth,
        zoneNumeric: data.overallZone === 'A' ? 1 : data.overallZone === 'B' ? 2 : data.overallZone === 'C' ? 3 : 4
      },
      target: data.overallZone === 'C' || data.overallZone === 'D' ? 1 : 0 // Binary classification for failure prediction
    };

    // Store ML training data
    const existingMLData = loadFromLocalStorage('mlTrainingData', []);
    existingMLData.push(mlFeatures);
    saveToLocalStorage('mlTrainingData', existingMLData);

    // Trigger model retraining if enough new data
    if (existingMLData.length % 10 === 0) {
      console.log('Triggering ML model retraining with new vibration data');
      // Would integrate with actual ML service
    }
  }, []);

  // Event subscription system
  const subscribeToVibrationUpdates = useCallback((equipmentId: string, callback: (data: VibrationDataPoint) => void) => {
    if (!subscribersRef.current.has(equipmentId)) {
      subscribersRef.current.set(equipmentId, new Set());
    }
    subscribersRef.current.get(equipmentId)!.add(callback);

    return () => {
      const subscribers = subscribersRef.current.get(equipmentId);
      if (subscribers) {
        subscribers.delete(callback);
        if (subscribers.size === 0) {
          subscribersRef.current.delete(equipmentId);
        }
      }
    };
  }, []);

  // Alert triggering
  const triggerVibrationAlert = useCallback((equipmentId: string, alertLevel: string, message: string) => {
    const equipment = equipment.find(eq => eq.id === equipmentId);
    const equipmentName = equipment?.name || `Equipment ${equipmentId}`;

    addNotification({
      title: `Vibration Alert - Zone ${alertLevel}`,
      message: `${equipmentName}: ${message}`,
      type: alertLevel === 'D' ? 'error' : 'warning',
      priority: alertLevel === 'D' ? 'high' : 'medium',
      persistent: alertLevel === 'D'
    });
  }, [equipment, addNotification]);

  // Batch updates for performance
  const batchUpdates = useCallback(async (updates: VibrationHistoryRecord[]): Promise<void> => {
    setIsProcessing(true);

    try {
      for (const record of updates) {
        await addVibrationReading(record);
      }
    } finally {
      setIsProcessing(false);
    }
  }, [addVibrationReading]);

  const contextValue: VibrationDataContextType = {
    vibrationData,
    addVibrationReading,
    getLatestVibrationData,
    getVibrationTrend,
    calculateVibrationMetrics,
    calculateOverallHealth,
    propagateToAssetManagement,
    propagateToMaintenance,
    propagateToFinancial,
    propagateToAnalytics,
    propagateToMLPipeline,
    subscribeToVibrationUpdates,
    triggerVibrationAlert,
    isProcessing,
    lastUpdateTimestamp,
    batchUpdates
  };

  return (
    <VibrationDataContext.Provider value={contextValue}>
      {children}
    </VibrationDataContext.Provider>
  );
};

export const useVibrationData = () => {
  const context = useContext(VibrationDataContext);
  if (context === undefined) {
    throw new Error('useVibrationData must be used within a VibrationDataProvider');
  }
  return context;
};
