/**
 * Real-time Vibration Data Flow Engine
 * Manages automatic propagation of vibration data across all EAMS modules
 */

import { VibrationHistoryRecord } from '@/data/vibrationHistoryData';
import { VibrationDataPoint } from '@/contexts/VibrationDataContext';
import { 
  performVibrationAnalysis, 
  calculateHealthScore, 
  generateMaintenanceRecommendations,
  calculateCostImpact 
} from '@/utils/vibrationCalculations';

export interface DataFlowEvent {
  id: string;
  type: 'vibration_update' | 'alert_triggered' | 'work_order_created' | 'ml_update';
  equipmentId: string;
  timestamp: string;
  data: any;
  propagatedTo: string[];
  status: 'pending' | 'processing' | 'completed' | 'failed';
}

export interface PropagationResult {
  module: string;
  success: boolean;
  duration: number;
  error?: string;
}

export interface DataFlowMetrics {
  totalEvents: number;
  successfulPropagations: number;
  failedPropagations: number;
  averageProcessingTime: number;
  modulePerformance: Record<string, {
    successRate: number;
    averageTime: number;
  }>;
}

class VibrationDataFlowEngine {
  private eventQueue: DataFlowEvent[] = [];
  private subscribers: Map<string, Set<(event: DataFlowEvent) => void>> = new Map();
  private metrics: DataFlowMetrics = {
    totalEvents: 0,
    successfulPropagations: 0,
    failedPropagations: 0,
    averageProcessingTime: 0,
    modulePerformance: {}
  };
  private isProcessing = false;

  /**
   * Process new vibration reading and propagate to all modules
   */
  async processVibrationReading(record: VibrationHistoryRecord): Promise<VibrationDataPoint> {
    const startTime = Date.now();
    const eventId = `vib-${record.equipmentId}-${Date.now()}`;
    
    try {
      // Create data flow event
      const event: DataFlowEvent = {
        id: eventId,
        type: 'vibration_update',
        equipmentId: record.equipmentId,
        timestamp: new Date().toISOString(),
        data: record,
        propagatedTo: [],
        status: 'processing'
      };

      this.eventQueue.push(event);
      this.metrics.totalEvents++;

      // Perform comprehensive vibration analysis
      const vibrationPoint = await this.analyzeVibrationData(record);

      // Propagate to all modules in parallel
      const propagationPromises = [
        this.propagateToAssetManagement(vibrationPoint),
        this.propagateToConditionMonitoring(vibrationPoint),
        this.propagateToMaintenance(vibrationPoint),
        this.propagateToFinancial(vibrationPoint),
        this.propagateToAnalytics(vibrationPoint),
        this.propagateToMLPipeline(vibrationPoint)
      ];

      const results = await Promise.allSettled(propagationPromises);
      
      // Update event status and metrics
      event.status = 'completed';
      event.propagatedTo = results.map((_, index) => this.getModuleName(index));
      
      const processingTime = Date.now() - startTime;
      this.updateMetrics(results, processingTime);

      // Notify subscribers
      this.notifySubscribers(event);

      // Trigger alerts if necessary
      if (vibrationPoint.overallZone === 'C' || vibrationPoint.overallZone === 'D') {
        await this.triggerAlert(vibrationPoint);
      }

      return vibrationPoint;

    } catch (error) {
      console.error('Error processing vibration reading:', error);
      const event = this.eventQueue.find(e => e.id === eventId);
      if (event) {
        event.status = 'failed';
      }
      throw error;
    }
  }

  /**
   * Analyze vibration data and create comprehensive data point
   */
  private async analyzeVibrationData(record: VibrationHistoryRecord): Promise<VibrationDataPoint> {
    // Analyze each measurement point
    const pumpNDEAnalysis = performVibrationAnalysis(record.pumpData?.nde || {});
    const pumpDEAnalysis = performVibrationAnalysis(record.pumpData?.de || {});
    const motorNDEAnalysis = performVibrationAnalysis(record.motorData?.nde || {});
    const motorDEAnalysis = performVibrationAnalysis(record.motorData?.de || {});

    // Calculate overall health
    const overallHealth = calculateHealthScore(pumpNDEAnalysis, 25, 8760, 4380);

    // Determine worst zone
    const zones = [
      pumpNDEAnalysis.iso10816Zone.zone,
      pumpDEAnalysis.iso10816Zone.zone,
      motorNDEAnalysis.iso10816Zone.zone,
      motorDEAnalysis.iso10816Zone.zone
    ];
    const overallZone = zones.reduce((worst, current) => {
      const zoneOrder = { 'A': 1, 'B': 2, 'C': 3, 'D': 4 };
      return zoneOrder[current] > zoneOrder[worst] ? current : worst;
    }, 'A' as const);

    // Generate recommendations
    const recommendations = generateMaintenanceRecommendations(pumpNDEAnalysis, overallHealth);

    // Calculate cost impact
    const costImpact = calculateCostImpact(pumpNDEAnalysis, 100000);

    return {
      equipmentId: record.equipmentId,
      timestamp: record.date,
      pumpNDE: {
        rmsVelocity: pumpNDEAnalysis.rmsVelocity,
        iso10816Zone: pumpNDEAnalysis.iso10816Zone.zone,
        healthScore: calculateHealthScore(pumpNDEAnalysis),
        trendDirection: pumpNDEAnalysis.timeSeriesAnalysis.trend,
        alertLevel: this.determineAlertLevel(pumpNDEAnalysis.iso10816Zone.zone)
      },
      pumpDE: {
        rmsVelocity: pumpDEAnalysis.rmsVelocity,
        iso10816Zone: pumpDEAnalysis.iso10816Zone.zone,
        healthScore: calculateHealthScore(pumpDEAnalysis),
        trendDirection: pumpDEAnalysis.timeSeriesAnalysis.trend,
        alertLevel: this.determineAlertLevel(pumpDEAnalysis.iso10816Zone.zone)
      },
      motorNDE: {
        rmsVelocity: motorNDEAnalysis.rmsVelocity,
        iso10816Zone: motorNDEAnalysis.iso10816Zone.zone,
        healthScore: calculateHealthScore(motorNDEAnalysis),
        trendDirection: motorNDEAnalysis.timeSeriesAnalysis.trend,
        alertLevel: this.determineAlertLevel(motorNDEAnalysis.iso10816Zone.zone)
      },
      motorDE: {
        rmsVelocity: motorDEAnalysis.rmsVelocity,
        iso10816Zone: motorDEAnalysis.iso10816Zone.zone,
        healthScore: calculateHealthScore(motorDEAnalysis),
        trendDirection: motorDEAnalysis.timeSeriesAnalysis.trend,
        alertLevel: this.determineAlertLevel(motorDEAnalysis.iso10816Zone.zone)
      },
      overallHealth,
      overallZone,
      maintenanceRecommendation: recommendations[0] || 'Continue normal operation',
      costImpact: costImpact.immediateCost
    };
  }

  /**
   * Propagate data to Asset Management module
   */
  private async propagateToAssetManagement(data: VibrationDataPoint): Promise<PropagationResult> {
    const startTime = Date.now();
    try {
      // Update asset health scores and condition status
      const assetUpdate = {
        equipmentId: data.equipmentId,
        healthScore: data.overallHealth,
        conditionStatus: data.overallZone,
        lastVibrationReading: data.timestamp,
        vibrationTrend: data.pumpNDE.trendDirection,
        nextInspectionDate: this.calculateNextInspection(data.overallZone)
      };

      // Store asset update
      localStorage.setItem(`asset-update-${data.equipmentId}`, JSON.stringify(assetUpdate));

      return {
        module: 'AssetManagement',
        success: true,
        duration: Date.now() - startTime
      };
    } catch (error) {
      return {
        module: 'AssetManagement',
        success: false,
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Propagate data to Condition Monitoring module
   */
  private async propagateToConditionMonitoring(data: VibrationDataPoint): Promise<PropagationResult> {
    const startTime = Date.now();
    try {
      // Update condition monitoring dashboards
      const conditionUpdate = {
        equipmentId: data.equipmentId,
        timestamp: data.timestamp,
        vibrationData: {
          pumpNDE: data.pumpNDE,
          pumpDE: data.pumpDE,
          motorNDE: data.motorNDE,
          motorDE: data.motorDE
        },
        overallCondition: data.overallZone,
        alerts: data.overallZone === 'C' || data.overallZone === 'D' ? [data.maintenanceRecommendation] : []
      };

      localStorage.setItem(`condition-update-${data.equipmentId}`, JSON.stringify(conditionUpdate));

      return {
        module: 'ConditionMonitoring',
        success: true,
        duration: Date.now() - startTime
      };
    } catch (error) {
      return {
        module: 'ConditionMonitoring',
        success: false,
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Propagate data to Maintenance module
   */
  private async propagateToMaintenance(data: VibrationDataPoint): Promise<PropagationResult> {
    const startTime = Date.now();
    try {
      // Auto-create work orders for critical conditions
      if (data.overallZone === 'C' || data.overallZone === 'D') {
        const workOrder = {
          id: `WO-VIB-${data.equipmentId}-${Date.now()}`,
          equipmentId: data.equipmentId,
          title: `Vibration Alert - Zone ${data.overallZone}`,
          description: data.maintenanceRecommendation,
          priority: data.overallZone === 'D' ? 'Critical' : 'High',
          type: 'Corrective',
          status: 'Open',
          createdDate: new Date().toISOString(),
          estimatedCost: data.costImpact,
          vibrationData: data
        };

        localStorage.setItem(`work-order-${workOrder.id}`, JSON.stringify(workOrder));
      }

      return {
        module: 'Maintenance',
        success: true,
        duration: Date.now() - startTime
      };
    } catch (error) {
      return {
        module: 'Maintenance',
        success: false,
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Propagate data to Financial module
   */
  private async propagateToFinancial(data: VibrationDataPoint): Promise<PropagationResult> {
    const startTime = Date.now();
    try {
      const financialImpact = {
        equipmentId: data.equipmentId,
        timestamp: data.timestamp,
        immediateMaintenanceCost: data.costImpact,
        projectedAnnualCost: data.costImpact * (data.overallZone === 'D' ? 4 : data.overallZone === 'C' ? 2 : 1),
        riskLevel: data.overallZone,
        healthScore: data.overallHealth,
        costAvoidance: data.overallZone === 'A' ? 10000 : 0 // Cost avoided by good condition
      };

      localStorage.setItem(`financial-impact-${data.equipmentId}`, JSON.stringify(financialImpact));

      return {
        module: 'Financial',
        success: true,
        duration: Date.now() - startTime
      };
    } catch (error) {
      return {
        module: 'Financial',
        success: false,
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Propagate data to Analytics module
   */
  private async propagateToAnalytics(data: VibrationDataPoint): Promise<PropagationResult> {
    const startTime = Date.now();
    try {
      const analyticsData = {
        equipmentId: data.equipmentId,
        timestamp: data.timestamp,
        kpis: {
          overallEquipmentEffectiveness: Math.max(0, data.overallHealth - 10),
          meanTimeBetweenFailures: data.overallZone === 'A' ? 8760 : data.overallZone === 'B' ? 4380 : 2190,
          meanTimeToRepair: data.overallZone === 'A' ? 2 : data.overallZone === 'B' ? 4 : 8,
          availability: data.overallHealth / 100,
          reliability: data.overallHealth / 100
        },
        trends: {
          vibrationTrend: data.pumpNDE.trendDirection,
          healthTrend: data.overallHealth > 80 ? 'improving' : data.overallHealth < 60 ? 'degrading' : 'stable'
        }
      };

      localStorage.setItem(`analytics-data-${data.equipmentId}`, JSON.stringify(analyticsData));

      return {
        module: 'Analytics',
        success: true,
        duration: Date.now() - startTime
      };
    } catch (error) {
      return {
        module: 'Analytics',
        success: false,
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Propagate data to ML Pipeline
   */
  private async propagateToMLPipeline(data: VibrationDataPoint): Promise<PropagationResult> {
    const startTime = Date.now();
    try {
      const mlFeatures = {
        equipmentId: data.equipmentId,
        timestamp: data.timestamp,
        features: {
          pumpNDE_rms: data.pumpNDE.rmsVelocity,
          pumpDE_rms: data.pumpDE.rmsVelocity,
          motorNDE_rms: data.motorNDE.rmsVelocity,
          motorDE_rms: data.motorDE.rmsVelocity,
          overallHealth: data.overallHealth,
          zoneNumeric: data.overallZone === 'A' ? 1 : data.overallZone === 'B' ? 2 : data.overallZone === 'C' ? 3 : 4
        },
        target: data.overallZone === 'C' || data.overallZone === 'D' ? 1 : 0
      };

      localStorage.setItem(`ml-features-${data.equipmentId}`, JSON.stringify(mlFeatures));

      return {
        module: 'MLPipeline',
        success: true,
        duration: Date.now() - startTime
      };
    } catch (error) {
      return {
        module: 'MLPipeline',
        success: false,
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Trigger alert for critical vibration conditions
   */
  private async triggerAlert(data: VibrationDataPoint): Promise<void> {
    const alertEvent: DataFlowEvent = {
      id: `alert-${data.equipmentId}-${Date.now()}`,
      type: 'alert_triggered',
      equipmentId: data.equipmentId,
      timestamp: new Date().toISOString(),
      data: {
        alertLevel: data.overallZone,
        message: data.maintenanceRecommendation,
        vibrationData: data
      },
      propagatedTo: ['NotificationSystem'],
      status: 'completed'
    };

    this.eventQueue.push(alertEvent);
    this.notifySubscribers(alertEvent);
  }

  // Helper methods
  private determineAlertLevel(zone: 'A' | 'B' | 'C' | 'D'): 'none' | 'warning' | 'critical' | 'emergency' {
    return zone === 'A' ? 'none' : zone === 'B' ? 'warning' : zone === 'C' ? 'critical' : 'emergency';
  }

  private calculateNextInspection(zone: 'A' | 'B' | 'C' | 'D'): string {
    const now = new Date();
    const daysToAdd = zone === 'A' ? 90 : zone === 'B' ? 60 : zone === 'C' ? 30 : 7;
    now.setDate(now.getDate() + daysToAdd);
    return now.toISOString().split('T')[0];
  }

  private getModuleName(index: number): string {
    const modules = ['AssetManagement', 'ConditionMonitoring', 'Maintenance', 'Financial', 'Analytics', 'MLPipeline'];
    return modules[index] || 'Unknown';
  }

  private updateMetrics(results: PromiseSettledResult<PropagationResult>[], processingTime: number): void {
    results.forEach(result => {
      if (result.status === 'fulfilled' && result.value.success) {
        this.metrics.successfulPropagations++;
      } else {
        this.metrics.failedPropagations++;
      }
    });

    this.metrics.averageProcessingTime = 
      (this.metrics.averageProcessingTime + processingTime) / 2;
  }

  private notifySubscribers(event: DataFlowEvent): void {
    const subscribers = this.subscribers.get(event.equipmentId) || new Set();
    subscribers.forEach(callback => callback(event));
  }

  /**
   * Subscribe to data flow events
   */
  subscribe(equipmentId: string, callback: (event: DataFlowEvent) => void): () => void {
    if (!this.subscribers.has(equipmentId)) {
      this.subscribers.set(equipmentId, new Set());
    }
    this.subscribers.get(equipmentId)!.add(callback);

    return () => {
      const subscribers = this.subscribers.get(equipmentId);
      if (subscribers) {
        subscribers.delete(callback);
        if (subscribers.size === 0) {
          this.subscribers.delete(equipmentId);
        }
      }
    };
  }

  /**
   * Get data flow metrics
   */
  getMetrics(): DataFlowMetrics {
    return { ...this.metrics };
  }

  /**
   * Get event queue status
   */
  getEventQueue(): DataFlowEvent[] {
    return [...this.eventQueue];
  }
}

// Export singleton instance
export const vibrationDataFlowEngine = new VibrationDataFlowEngine();
