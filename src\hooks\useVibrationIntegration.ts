/**
 * Custom hook for integrating vibration data with the unified workflow system
 * Provides seamless integration between VibrationDataEntryForm and the data flow engine
 */

import { useState, useCallback, useEffect, useRef } from 'react';
import { useVibrationData } from '@/contexts/VibrationDataContext';
import { useAssetContext } from '@/contexts/AssetContext';
import { useNotifications } from '@/components/ui/notification-system';
import { vibrationDataFlowEngine } from '@/services/vibrationDataFlowEngine';
import { VibrationHistoryRecord } from '@/data/vibrationHistoryData';
import { calculateRMSVelocity, getISO10816Zone, ISO10816_ZONES } from '@/utils/vibrationCalculations';

export interface VibrationFormState {
  isSubmitting: boolean;
  isProcessing: boolean;
  lastSubmissionTime: number | null;
  validationErrors: Record<string, string>;
  realTimeCalculations: {
    pumpNDE: { rms: number; zone: string; };
    pumpDE: { rms: number; zone: string; };
    motorNDE: { rms: number; zone: string; };
    motorDE: { rms: number; zone: string; };
    overall: { health: number; zone: string; recommendation: string; };
  };
}

export interface VibrationSubmissionResult {
  success: boolean;
  vibrationDataPoint?: any;
  propagationResults?: any[];
  error?: string;
  processingTime: number;
}

export interface UseVibrationIntegrationOptions {
  enableRealTimeCalculations?: boolean;
  enableAutoSave?: boolean;
  autoSaveInterval?: number;
  enableSpeechFeedback?: boolean;
  enableVisualFeedback?: boolean;
}

export function useVibrationIntegration(options: UseVibrationIntegrationOptions = {}) {
  const {
    enableRealTimeCalculations = true,
    enableAutoSave = false,
    autoSaveInterval = 30000,
    enableSpeechFeedback = true,
    enableVisualFeedback = true
  } = options;

  const { addVibrationReading, isProcessing: contextProcessing } = useVibrationData();
  const { addVibrationHistoryEntry, triggerDataUpdate } = useAssetContext();
  const { addNotification } = useNotifications();

  // State management
  const [formState, setFormState] = useState<VibrationFormState>({
    isSubmitting: false,
    isProcessing: false,
    lastSubmissionTime: null,
    validationErrors: {},
    realTimeCalculations: {
      pumpNDE: { rms: 0, zone: 'A' },
      pumpDE: { rms: 0, zone: 'A' },
      motorNDE: { rms: 0, zone: 'A' },
      motorDE: { rms: 0, zone: 'A' },
      overall: { health: 100, zone: 'A', recommendation: 'Continue normal operation' }
    }
  });

  // Refs for performance optimization
  const calculationTimeoutRef = useRef<NodeJS.Timeout>();
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout>();
  const speechSynthesisRef = useRef<SpeechSynthesis | null>(null);

  // Initialize speech synthesis
  useEffect(() => {
    if (enableSpeechFeedback && 'speechSynthesis' in window) {
      speechSynthesisRef.current = window.speechSynthesis;
    }
  }, [enableSpeechFeedback]);

  /**
   * Calculate real-time vibration metrics
   */
  const calculateRealTimeMetrics = useCallback((formValues: any) => {
    if (!enableRealTimeCalculations) return;

    // Clear previous timeout
    if (calculationTimeoutRef.current) {
      clearTimeout(calculationTimeoutRef.current);
    }

    // Debounce calculations
    calculationTimeoutRef.current = setTimeout(() => {
      try {
        // Calculate RMS and zones for each measurement point
        const pumpNDERMS = calculateRMSVelocity(formValues.pump?.nde || {});
        const pumpDERMS = calculateRMSVelocity(formValues.pump?.de || {});
        const motorNDERMS = calculateRMSVelocity(formValues.motor?.nde || {});
        const motorDERMS = calculateRMSVelocity(formValues.motor?.de || {});

        const pumpNDEZone = getISO10816Zone(pumpNDERMS);
        const pumpDEZone = getISO10816Zone(pumpDERMS);
        const motorNDEZone = getISO10816Zone(motorNDERMS);
        const motorDEZone = getISO10816Zone(motorDERMS);

        // Calculate overall metrics
        const allRMS = [pumpNDERMS, pumpDERMS, motorNDERMS, motorDERMS];
        const allZones = [pumpNDEZone, pumpDEZone, motorNDEZone, motorDEZone];
        
        const overallHealth = allZones.reduce((sum, zone) => sum + zone.healthScore, 0) / allZones.length;
        const worstZone = allZones.reduce((worst, current) => {
          const worstIndex = ISO10816_ZONES.findIndex(z => z.zone === worst.zone);
          const currentIndex = ISO10816_ZONES.findIndex(z => z.zone === current.zone);
          return currentIndex > worstIndex ? current : worst;
        });

        const recommendation = worstZone.recommendation;

        // Update state
        setFormState(prev => ({
          ...prev,
          realTimeCalculations: {
            pumpNDE: { rms: pumpNDERMS, zone: pumpNDEZone.zone },
            pumpDE: { rms: pumpDERMS, zone: pumpDEZone.zone },
            motorNDE: { rms: motorNDERMS, zone: motorNDEZone.zone },
            motorDE: { rms: motorDERMS, zone: motorDEZone.zone },
            overall: { health: overallHealth, zone: worstZone.zone, recommendation }
          }
        }));

        // Provide speech feedback for critical conditions
        if (enableSpeechFeedback && worstZone.zone === 'D') {
          provideSpeechFeedback(`Critical vibration detected. Zone ${worstZone.zone}. ${recommendation}`);
        }

        // Provide visual feedback
        if (enableVisualFeedback && (worstZone.zone === 'C' || worstZone.zone === 'D')) {
          addNotification({
            title: `Vibration Alert - Zone ${worstZone.zone}`,
            message: recommendation,
            type: worstZone.zone === 'D' ? 'error' : 'warning',
            duration: worstZone.zone === 'D' ? 0 : 5000 // Persistent for critical alerts
          });
        }

      } catch (error) {
        console.error('Error calculating real-time metrics:', error);
      }
    }, 300); // 300ms debounce
  }, [enableRealTimeCalculations, enableSpeechFeedback, enableVisualFeedback, addNotification]);

  /**
   * Provide speech feedback
   */
  const provideSpeechFeedback = useCallback((message: string) => {
    if (!speechSynthesisRef.current) return;

    try {
      // Cancel any ongoing speech
      speechSynthesisRef.current.cancel();

      const utterance = new SpeechSynthesisUtterance(message);
      
      // Try to use a female voice
      const voices = speechSynthesisRef.current.getVoices();
      const femaleVoice = voices.find(voice => 
        voice.name.toLowerCase().includes('female') || 
        voice.name.toLowerCase().includes('woman') ||
        voice.name.toLowerCase().includes('zira') ||
        voice.name.toLowerCase().includes('hazel')
      );

      if (femaleVoice) {
        utterance.voice = femaleVoice;
      }

      utterance.rate = 0.9;
      utterance.pitch = 1.1;
      utterance.volume = 0.8;

      speechSynthesisRef.current.speak(utterance);
    } catch (error) {
      console.error('Error providing speech feedback:', error);
    }
  }, []);

  /**
   * Validate vibration form data
   */
  const validateFormData = useCallback((data: any): Record<string, string> => {
    const errors: Record<string, string> = {};

    // Required fields validation
    if (!data.equipmentId && !data.pumpId && !data.motorId) {
      errors.equipment = 'Equipment selection is required';
    }

    if (!data.date) {
      errors.date = 'Date is required';
    }

    // Vibration data validation
    const hasVibrationData = 
      (data.pump?.nde && Object.values(data.pump.nde).some(v => v !== '' && v !== null)) ||
      (data.pump?.de && Object.values(data.pump.de).some(v => v !== '' && v !== null)) ||
      (data.motor?.nde && Object.values(data.motor.nde).some(v => v !== '' && v !== null)) ||
      (data.motor?.de && Object.values(data.motor.de).some(v => v !== '' && v !== null));

    if (!hasVibrationData) {
      errors.vibrationData = 'At least one vibration measurement is required';
    }

    // Range validation for vibration values
    const validateVibrationRange = (values: any, prefix: string) => {
      if (values) {
        Object.entries(values).forEach(([key, value]) => {
          const numValue = Number(value);
          if (value !== '' && value !== null && !isNaN(numValue)) {
            if (numValue < 0) {
              errors[`${prefix}.${key}`] = 'Vibration values cannot be negative';
            }
            if (numValue > 50) {
              errors[`${prefix}.${key}`] = 'Vibration value seems unusually high (>50 mm/s)';
            }
          }
        });
      }
    };

    validateVibrationRange(data.pump?.nde, 'pump.nde');
    validateVibrationRange(data.pump?.de, 'pump.de');
    validateVibrationRange(data.motor?.nde, 'motor.nde');
    validateVibrationRange(data.motor?.de, 'motor.de');

    return errors;
  }, []);

  /**
   * Submit vibration data with full integration
   */
  const submitVibrationData = useCallback(async (formData: any): Promise<VibrationSubmissionResult> => {
    const startTime = Date.now();
    
    setFormState(prev => ({ ...prev, isSubmitting: true, isProcessing: true }));

    try {
      // Validate form data
      const validationErrors = validateFormData(formData);
      if (Object.keys(validationErrors).length > 0) {
        setFormState(prev => ({ ...prev, validationErrors }));
        return {
          success: false,
          error: 'Validation failed',
          processingTime: Date.now() - startTime
        };
      }

      // Create vibration history record
      const vibrationRecord: VibrationHistoryRecord = {
        id: `VH-${Date.now()}`,
        equipmentId: formData.equipmentId || formData.pumpId || formData.motorId || '',
        date: formData.date,
        pumpData: formData.pump || {},
        motorData: formData.motor || {},
        positions: formData.positions || {},
        zone: formData.zone || '',
        pumpNo: formData.pumpNo || '',
        motorBrand: formData.motorBrand || '',
        serialNumbers: formData.serialNumbers || '',
        project: formData.project || '',
        pumpStation: formData.pumpStation || '',
        pumpBrand: formData.pumpBrand || '',
        operationHr: formData.operationHr || '',
        operationPower: formData.operationPower || '',
        pumpHead: formData.pumpHead || '',
        pumpFlowRate: formData.pumpFlowRate || '',
        dischargeP: formData.dischargeP || '',
        mainHeaderP: formData.mainHeaderP || '',
        suctionP: formData.suctionP || '',
        fatPumpPower: formData.fatPumpPower || '',
        ratedMotorPower: formData.ratedMotorPower || '',
        enteredBy: formData.enteredBy || 'System User',
        // Map all vibration fields
        pumpNDE_bv: formData.pump?.nde?.bv || '',
        pumpNDE_bg: formData.pump?.nde?.bg || '',
        pumpNDE_accV: formData.pump?.nde?.accV || '',
        pumpNDE_accH: formData.pump?.nde?.accH || '',
        pumpNDE_accAxl: formData.pump?.nde?.accAxl || '',
        pumpNDE_velV: formData.pump?.nde?.velV || '',
        pumpNDE_velH: formData.pump?.nde?.velH || '',
        pumpNDE_velAxl: formData.pump?.nde?.velAxl || '',
        pumpNDE_temp: formData.pump?.nde?.temp || '',
        pumpDE_bv: formData.pump?.de?.bv || '',
        pumpDE_bg: formData.pump?.de?.bg || '',
        pumpDE_accV: formData.pump?.de?.accV || '',
        pumpDE_accH: formData.pump?.de?.accH || '',
        pumpDE_accAxl: formData.pump?.de?.accAxl || '',
        pumpDE_velV: formData.pump?.de?.velV || '',
        pumpDE_velH: formData.pump?.de?.velH || '',
        pumpDE_velAxl: formData.pump?.de?.velAxl || '',
        pumpDE_temp: formData.pump?.de?.temp || '',
        motorNDE_bv: formData.motor?.nde?.bv || '',
        motorNDE_bg: formData.motor?.nde?.bg || '',
        motorNDE_accV: formData.motor?.nde?.accV || '',
        motorNDE_accH: formData.motor?.nde?.accH || '',
        motorNDE_accAxl: formData.motor?.nde?.accAxl || '',
        motorNDE_velV: formData.motor?.nde?.velV || '',
        motorNDE_velH: formData.motor?.nde?.velH || '',
        motorNDE_velAxl: formData.motor?.nde?.velAxl || '',
        motorNDE_temp: formData.motor?.nde?.temp || '',
        motorDE_bv: formData.motor?.de?.bv || '',
        motorDE_bg: formData.motor?.de?.bg || '',
        motorDE_accV: formData.motor?.de?.accV || '',
        motorDE_accH: formData.motor?.de?.accH || '',
        motorDE_accAxl: formData.motor?.de?.accAxl || '',
        motorDE_velV: formData.motor?.de?.velV || '',
        motorDE_velH: formData.motor?.de?.velH || '',
        motorDE_velAxl: formData.motor?.de?.velAxl || '',
        motorDE_temp: formData.motor?.de?.temp || '',
        vibrationRMS: formState.realTimeCalculations.overall.health / 10 // Convert health to RMS approximation
      };

      // Process through the unified data flow engine
      const vibrationDataPoint = await vibrationDataFlowEngine.processVibrationReading(vibrationRecord);

      // Add to context (for backward compatibility)
      addVibrationHistoryEntry(vibrationRecord);

      // Trigger global data update
      triggerDataUpdate();

      // Update form state
      setFormState(prev => ({
        ...prev,
        lastSubmissionTime: Date.now(),
        validationErrors: {}
      }));

      // Provide success feedback
      addNotification({
        title: 'Vibration Data Submitted',
        message: `Data processed successfully for equipment ${vibrationRecord.equipmentId}`,
        type: 'success'
      });

      // Provide speech feedback for successful submission
      if (enableSpeechFeedback) {
        provideSpeechFeedback(`Vibration data submitted successfully. Overall condition: Zone ${vibrationDataPoint.overallZone}`);
      }

      return {
        success: true,
        vibrationDataPoint,
        processingTime: Date.now() - startTime
      };

    } catch (error) {
      console.error('Error submitting vibration data:', error);
      
      addNotification({
        title: 'Submission Error',
        message: 'Failed to submit vibration data. Please try again.',
        type: 'error'
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        processingTime: Date.now() - startTime
      };
    } finally {
      setFormState(prev => ({ ...prev, isSubmitting: false, isProcessing: false }));
    }
  }, [
    validateFormData, 
    addVibrationHistoryEntry, 
    triggerDataUpdate, 
    addNotification, 
    enableSpeechFeedback, 
    provideSpeechFeedback,
    formState.realTimeCalculations.overall.health
  ]);

  /**
   * Auto-save functionality
   */
  const autoSave = useCallback((formData: any) => {
    if (!enableAutoSave) return;

    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }

    autoSaveTimeoutRef.current = setTimeout(() => {
      try {
        localStorage.setItem('vibration-form-autosave', JSON.stringify({
          data: formData,
          timestamp: Date.now()
        }));
      } catch (error) {
        console.error('Auto-save failed:', error);
      }
    }, autoSaveInterval);
  }, [enableAutoSave, autoSaveInterval]);

  /**
   * Load auto-saved data
   */
  const loadAutoSavedData = useCallback(() => {
    try {
      const saved = localStorage.getItem('vibration-form-autosave');
      if (saved) {
        const { data, timestamp } = JSON.parse(saved);
        // Only load if saved within last 24 hours
        if (Date.now() - timestamp < 24 * 60 * 60 * 1000) {
          return data;
        }
      }
    } catch (error) {
      console.error('Failed to load auto-saved data:', error);
    }
    return null;
  }, []);

  /**
   * Clear auto-saved data
   */
  const clearAutoSavedData = useCallback(() => {
    try {
      localStorage.removeItem('vibration-form-autosave');
    } catch (error) {
      console.error('Failed to clear auto-saved data:', error);
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (calculationTimeoutRef.current) {
        clearTimeout(calculationTimeoutRef.current);
      }
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
      if (speechSynthesisRef.current) {
        speechSynthesisRef.current.cancel();
      }
    };
  }, []);

  return {
    // State
    formState,
    isProcessing: formState.isProcessing || contextProcessing,
    
    // Actions
    submitVibrationData,
    calculateRealTimeMetrics,
    provideSpeechFeedback,
    autoSave,
    loadAutoSavedData,
    clearAutoSavedData,
    
    // Utilities
    validateFormData,
    
    // Real-time calculations
    realTimeCalculations: formState.realTimeCalculations
  };
}
