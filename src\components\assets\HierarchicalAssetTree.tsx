/**
 * Hierarchical Asset Tree Navigation Component
 * Provides tree view with expand/collapse functionality and asset count badges
 */

import React, { useState, useMemo } from 'react';
import { ChevronRight, ChevronDown, Building2, Factory, Zap, Settings, Gauge, AlertTriangle, Plus, MoreHorizontal } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useHierarchicalAssets } from '@/contexts/HierarchicalAssetContext';
import { HierarchicalAsset, HierarchyLevel } from '@/types/hierarchicalAssets';
import { cn } from '@/lib/utils';

interface AssetTreeNodeProps {
  asset: HierarchicalAsset;
  level: number;
  isExpanded: boolean;
  isSelected: boolean;
  onToggleExpand: (assetId: string) => void;
  onSelect: (assetId: string) => void;
  onAddChild: (parentId: string) => void;
  onEdit: (assetId: string) => void;
  onDelete: (assetId: string) => void;
}

const getAssetIcon = (type: HierarchyLevel, category?: string) => {
  switch (type) {
    case 'zone':
      return <Building2 className="h-4 w-4 text-blue-600" />;
    case 'station':
      return <Factory className="h-4 w-4 text-green-600" />;
    case 'line':
    case 'system':
      return <Zap className="h-4 w-4 text-orange-600" />;
    case 'equipment':
      switch (category) {
        case 'pump':
          return <Settings className="h-4 w-4 text-purple-600" />;
        case 'motor':
          return <Zap className="h-4 w-4 text-red-600" />;
        case 'valve':
          return <Gauge className="h-4 w-4 text-gray-600" />;
        case 'sensor':
          return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
        default:
          return <Settings className="h-4 w-4 text-gray-600" />;
      }
    default:
      return <Settings className="h-4 w-4 text-gray-600" />;
  }
};

const getConditionColor = (condition?: string) => {
  switch (condition) {
    case 'excellent':
      return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
    case 'good':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
    case 'fair':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
    case 'poor':
      return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
    case 'critical':
      return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
  }
};

const AssetTreeNode: React.FC<AssetTreeNodeProps> = ({
  asset,
  level,
  isExpanded,
  isSelected,
  onToggleExpand,
  onSelect,
  onAddChild,
  onEdit,
  onDelete
}) => {
  const hasChildren = asset.children && asset.children.length > 0;
  const childCount = asset.children?.length || 0;
  
  // Calculate equipment count for non-equipment nodes
  const equipmentCount = useMemo(() => {
    if (asset.type === 'equipment') return 0;
    
    const countEquipment = (node: HierarchicalAsset): number => {
      let count = node.type === 'equipment' ? 1 : 0;
      if (node.children) {
        count += node.children.reduce((sum, child) => sum + countEquipment(child), 0);
      }
      return count;
    };
    
    return countEquipment(asset);
  }, [asset]);

  return (
    <div className="select-none">
      {/* Node Row */}
      <div
        className={cn(
          "flex items-center gap-2 py-1 px-2 rounded-md cursor-pointer hover:bg-accent/50 transition-colors",
          isSelected && "bg-accent",
          level > 0 && "ml-4"
        )}
        style={{ paddingLeft: `${level * 16 + 8}px` }}
        onClick={() => onSelect(asset.id)}
      >
        {/* Expand/Collapse Button */}
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0"
          onClick={(e) => {
            e.stopPropagation();
            if (hasChildren) {
              onToggleExpand(asset.id);
            }
          }}
        >
          {hasChildren ? (
            isExpanded ? (
              <ChevronDown className="h-3 w-3" />
            ) : (
              <ChevronRight className="h-3 w-3" />
            )
          ) : (
            <div className="h-3 w-3" />
          )}
        </Button>

        {/* Asset Icon */}
        {getAssetIcon(asset.type, asset.category)}

        {/* Asset Name */}
        <span className="flex-1 text-sm font-medium truncate">
          {asset.name}
        </span>

        {/* Asset Count Badge */}
        {asset.type !== 'equipment' && (
          <Badge variant="secondary" className="text-xs">
            {equipmentCount} eq
          </Badge>
        )}

        {/* Condition Badge for Equipment */}
        {asset.type === 'equipment' && asset.condition && (
          <Badge className={cn("text-xs", getConditionColor(asset.condition))}>
            {asset.condition}
          </Badge>
        )}

        {/* Actions Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100"
              onClick={(e) => e.stopPropagation()}
            >
              <MoreHorizontal className="h-3 w-3" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {asset.type !== 'equipment' && (
              <DropdownMenuItem onClick={() => onAddChild(asset.id)}>
                <Plus className="h-4 w-4 mr-2" />
                Add Child
              </DropdownMenuItem>
            )}
            <DropdownMenuItem onClick={() => onEdit(asset.id)}>
              <Settings className="h-4 w-4 mr-2" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => onDelete(asset.id)}
              className="text-red-600"
            >
              <AlertTriangle className="h-4 w-4 mr-2" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Children */}
      {hasChildren && isExpanded && (
        <div className="ml-2">
          {asset.children!.map((child) => (
            <AssetTreeNode
              key={child.id}
              asset={child}
              level={level + 1}
              isExpanded={isExpanded}
              isSelected={isSelected}
              onToggleExpand={onToggleExpand}
              onSelect={onSelect}
              onAddChild={onAddChild}
              onEdit={onEdit}
              onDelete={onDelete}
            />
          ))}
        </div>
      )}
    </div>
  );
};

interface HierarchicalAssetTreeProps {
  onAssetSelect?: (asset: HierarchicalAsset) => void;
  onAssetEdit?: (asset: HierarchicalAsset) => void;
  onAssetDelete?: (asset: HierarchicalAsset) => void;
  onAddAsset?: (parentId: string) => void;
  className?: string;
}

export const HierarchicalAssetTree: React.FC<HierarchicalAssetTreeProps> = ({
  onAssetSelect,
  onAssetEdit,
  onAssetDelete,
  onAddAsset,
  className
}) => {
  const {
    assets,
    selectedAssetId,
    expandedNodes,
    treeOperations,
    getAssetById,
    hierarchyStats
  } = useHierarchicalAssets();

  const [searchTerm, setSearchTerm] = useState('');

  // Filter assets based on search term
  const filteredAssets = useMemo(() => {
    if (!searchTerm) return assets;

    const filterAssets = (assetList: HierarchicalAsset[]): HierarchicalAsset[] => {
      return assetList.reduce((filtered: HierarchicalAsset[], asset) => {
        const matchesSearch = asset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          asset.serialNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          asset.manufacturer?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          asset.model?.toLowerCase().includes(searchTerm.toLowerCase());

        const filteredChildren = asset.children ? filterAssets(asset.children) : [];

        if (matchesSearch || filteredChildren.length > 0) {
          filtered.push({
            ...asset,
            children: filteredChildren.length > 0 ? filteredChildren : asset.children
          });
        }

        return filtered;
      }, []);
    };

    return filterAssets(assets);
  }, [assets, searchTerm]);

  const handleToggleExpand = (assetId: string) => {
    if (expandedNodes.has(assetId)) {
      treeOperations.collapseNode(assetId);
    } else {
      treeOperations.expandNode(assetId);
    }
  };

  const handleSelect = (assetId: string) => {
    treeOperations.selectNode(assetId);
    const asset = getAssetById(assetId);
    if (asset && onAssetSelect) {
      onAssetSelect(asset);
    }
  };

  const handleAddChild = (parentId: string) => {
    if (onAddAsset) {
      onAddAsset(parentId);
    }
  };

  const handleEdit = (assetId: string) => {
    const asset = getAssetById(assetId);
    if (asset && onAssetEdit) {
      onAssetEdit(asset);
    }
  };

  const handleDelete = (assetId: string) => {
    const asset = getAssetById(assetId);
    if (asset && onAssetDelete) {
      onAssetDelete(asset);
    }
  };

  return (
    <Card className={cn("h-full", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          <Building2 className="h-5 w-5" />
          Asset Hierarchy
        </CardTitle>
        
        {/* Search */}
        <Input
          placeholder="Search assets..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="h-8"
        />

        {/* Summary Stats */}
        <div className="flex gap-2 text-xs text-muted-foreground">
          <span>{hierarchyStats.equipmentCount} Equipment</span>
          <span>•</span>
          <span>{hierarchyStats.stationCount} Stations</span>
          <span>•</span>
          <span>{hierarchyStats.zoneCount} Zones</span>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <div className="max-h-[600px] overflow-y-auto">
          {filteredAssets.map((asset) => (
            <AssetTreeNode
              key={asset.id}
              asset={asset}
              level={0}
              isExpanded={expandedNodes.has(asset.id)}
              isSelected={selectedAssetId === asset.id}
              onToggleExpand={handleToggleExpand}
              onSelect={handleSelect}
              onAddChild={handleAddChild}
              onEdit={handleEdit}
              onDelete={handleDelete}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
