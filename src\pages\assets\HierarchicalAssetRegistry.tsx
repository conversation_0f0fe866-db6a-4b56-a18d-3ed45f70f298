/**
 * Hierarchical Asset Registry
 * Enhanced asset management with hierarchical structure and dynamic calculations
 */

import React, { useState, useMemo } from 'react';
import { AppLayout } from "@/components/layout/AppLayout";
import { StatCard } from "@/components/dashboard/StatCard";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { 
  Plus, Download, Upload, Eye, Edit, Trash2,
  Settings, Activity, AlertTriangle, TrendingUp, Gauge, Thermometer, Zap,
  Wrench, Calendar, BarChart3, Cog, Package,
  Table as TableIcon, LayoutGrid, Filter, RefreshCw, Building2, Factory, ChevronRight
} from "lucide-react";
import { HierarchicalAssetTree } from "@/components/assets/HierarchicalAssetTree";
import { useHierarchicalAssets } from "@/contexts/HierarchicalAssetContext";
import { HierarchicalAsset } from "@/types/hierarchicalAssets";
import { performanceMetrics } from "@/utils/hierarchicalAssetCalculations";
import { cn } from "@/lib/utils";

const HierarchicalAssetRegistry = () => {
  const {
    assets,
    selectedAssetId,
    currentPath,
    hierarchyStats,
    getAssetById,
    getAssetsByParent,
    getEquipmentAssets,
    refreshAllCalculations
  } = useHierarchicalAssets();

  const [selectedAsset, setSelectedAsset] = useState<HierarchicalAsset | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showFormModal, setShowFormModal] = useState(false);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');
  const [activeTab, setActiveTab] = useState('overview');
  const [viewMode, setViewMode] = useState<'tree' | 'table' | 'cards'>('tree');
  const [searchTerm, setSearchTerm] = useState('');

  // Get current asset based on selection
  const currentAsset = selectedAssetId ? getAssetById(selectedAssetId) : null;
  
  // Get children of current asset or root assets
  const currentChildren = currentAsset ? (currentAsset.children || []) : assets;
  
  // Filter children based on search
  const filteredChildren = useMemo(() => {
    if (!searchTerm) return currentChildren;
    
    return currentChildren.filter(asset => 
      asset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      asset.serialNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      asset.manufacturer?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      asset.model?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [currentChildren, searchTerm]);

  // Calculate dynamic KPIs for display
  const kpiStats = useMemo(() => {
    const equipmentAssets = getEquipmentAssets();
    
    if (equipmentAssets.length === 0) {
      return {
        totalAssets: 0,
        averageAvailability: 0,
        totalMaintenanceCost: 0,
        criticalEquipment: 0
      };
    }

    const availabilities = equipmentAssets.map(asset => performanceMetrics.calculateAvailability(asset));
    const maintenanceCosts = equipmentAssets.map(asset => performanceMetrics.calculateMaintenanceCost(asset));
    const criticalCount = equipmentAssets.filter(asset => 
      asset.condition === 'critical' || asset.condition === 'poor'
    ).length;

    return {
      totalAssets: equipmentAssets.length,
      averageAvailability: availabilities.reduce((sum, val) => sum + val, 0) / availabilities.length,
      totalMaintenanceCost: maintenanceCosts.reduce((sum, val) => sum + val, 0),
      criticalEquipment: criticalCount
    };
  }, [getEquipmentAssets]);

  const handleAssetSelect = (asset: HierarchicalAsset) => {
    setSelectedAsset(asset);
  };

  const handleAssetEdit = (asset: HierarchicalAsset) => {
    setSelectedAsset(asset);
    setFormMode('edit');
    setShowFormModal(true);
  };

  const handleAssetDelete = (asset: HierarchicalAsset) => {
    // Implementation for asset deletion
    console.log('Delete asset:', asset.id);
  };

  const handleAddAsset = (parentId?: string) => {
    setSelectedAsset(parentId ? getAssetById(parentId) : null);
    setFormMode('create');
    setShowFormModal(true);
  };

  const getConditionColor = (condition?: string) => {
    switch (condition) {
      case 'excellent':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'good':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'fair':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'poor':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const renderBreadcrumb = () => {
    if (currentPath.length === 0) return null;

    return (
      <Breadcrumb className="mb-4">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="#" onClick={() => setSelectedAsset(null)}>
              Root
            </BreadcrumbLink>
          </BreadcrumbItem>
          {currentPath.map((pathItem, index) => (
            <React.Fragment key={index}>
              <BreadcrumbSeparator>
                <ChevronRight className="h-4 w-4" />
              </BreadcrumbSeparator>
              <BreadcrumbItem>
                {index === currentPath.length - 1 ? (
                  <BreadcrumbPage>{pathItem}</BreadcrumbPage>
                ) : (
                  <BreadcrumbLink href="#" onClick={() => {
                    // Navigate to this level
                    console.log('Navigate to:', pathItem);
                  }}>
                    {pathItem}
                  </BreadcrumbLink>
                )}
              </BreadcrumbItem>
            </React.Fragment>
          ))}
        </BreadcrumbList>
      </Breadcrumb>
    );
  };

  const renderAssetCard = (asset: HierarchicalAsset) => {
    const availability = asset.type === 'equipment' ? performanceMetrics.calculateAvailability(asset) : 0;
    const efficiency = asset.type === 'equipment' ? performanceMetrics.calculateEfficiency(asset) : 0;
    const maintenanceCost = asset.type === 'equipment' ? performanceMetrics.calculateMaintenanceCost(asset) : 0;

    return (
      <Card key={asset.id} className="cursor-pointer hover:shadow-md transition-shadow">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              {asset.type === 'zone' && <Building2 className="h-4 w-4 text-blue-600" />}
              {asset.type === 'station' && <Factory className="h-4 w-4 text-green-600" />}
              {asset.type === 'line' && <Zap className="h-4 w-4 text-orange-600" />}
              {asset.type === 'system' && <Zap className="h-4 w-4 text-orange-600" />}
              {asset.type === 'equipment' && <Settings className="h-4 w-4 text-purple-600" />}
              {asset.name}
            </CardTitle>
            {asset.condition && (
              <Badge className={cn("text-xs", getConditionColor(asset.condition))}>
                {asset.condition}
              </Badge>
            )}
          </div>
          <CardDescription className="text-xs">
            {asset.type === 'equipment' ? (
              `${asset.manufacturer} ${asset.model}`
            ) : (
              `${asset.children?.length || 0} child assets`
            )}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {asset.type === 'equipment' ? (
            <div className="space-y-2">
              <div className="flex justify-between text-xs">
                <span>Availability:</span>
                <span className="font-medium">{availability.toFixed(1)}%</span>
              </div>
              <div className="flex justify-between text-xs">
                <span>Efficiency:</span>
                <span className="font-medium">{efficiency.toFixed(1)}%</span>
              </div>
              <div className="flex justify-between text-xs">
                <span>Maintenance Cost:</span>
                <span className="font-medium">${maintenanceCost.toLocaleString()}</span>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <div className="flex justify-between text-xs">
                <span>Child Assets:</span>
                <span className="font-medium">{asset.children?.length || 0}</span>
              </div>
              <div className="flex justify-between text-xs">
                <span>Type:</span>
                <span className="font-medium capitalize">{asset.type}</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Hierarchical Asset Registry</h1>
            <p className="text-muted-foreground">
              Manage assets in a hierarchical structure with dynamic calculations
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={refreshAllCalculations}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh KPIs
            </Button>
            <Button onClick={() => handleAddAsset()}>
              <Plus className="h-4 w-4 mr-2" />
              Add Asset
            </Button>
          </div>
        </div>

        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <StatCard
            title="Total Equipment"
            value={kpiStats.totalAssets.toString()}
            icon={Package}
            trend={{ value: 0, isPositive: true }}
          />
          <StatCard
            title="Average Availability"
            value={`${kpiStats.averageAvailability.toFixed(1)}%`}
            icon={Gauge}
            trend={{ value: 0, isPositive: true }}
          />
          <StatCard
            title="Maintenance Cost"
            value={`$${kpiStats.totalMaintenanceCost.toLocaleString()}`}
            icon={Wrench}
            trend={{ value: 0, isPositive: false }}
          />
          <StatCard
            title="Critical Equipment"
            value={kpiStats.criticalEquipment.toString()}
            icon={AlertTriangle}
            trend={{ value: 0, isPositive: false }}
          />
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Asset Tree Sidebar */}
          <div className="lg:col-span-1">
            <HierarchicalAssetTree
              onAssetSelect={handleAssetSelect}
              onAssetEdit={handleAssetEdit}
              onAssetDelete={handleAssetDelete}
              onAddAsset={handleAddAsset}
            />
          </div>

          {/* Main Content Area */}
          <div className="lg:col-span-3">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Asset Details</CardTitle>
                    <CardDescription>
                      {currentAsset ? `Viewing ${currentAsset.name}` : 'Select an asset from the tree'}
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <Input
                      placeholder="Search assets..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-64"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setViewMode(viewMode === 'cards' ? 'table' : 'cards')}
                    >
                      {viewMode === 'cards' ? <TableIcon className="h-4 w-4" /> : <LayoutGrid className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
                {renderBreadcrumb()}
              </CardHeader>
              <CardContent>
                {filteredChildren.length > 0 ? (
                  <div className={cn(
                    viewMode === 'cards' 
                      ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
                      : "space-y-2"
                  )}>
                    {filteredChildren.map(asset => renderAssetCard(asset))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    {searchTerm ? 'No assets match your search' : 'No child assets found'}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  );
};

export default HierarchicalAssetRegistry;
