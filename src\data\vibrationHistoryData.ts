// Vibration history data structure for logging all vibration form submissions

export interface VibrationHistoryRecord {
    id: string;
    equipmentId: string;
    date: string;
    pumpData: any; // Store all pump fields from the form
    motorData: any; // Store all motor fields from the form
    positions: any; // Store all positions fields from the form
    zone: string;
    pumpNo: string;
    motorBrand: string;
    serialNumbers: string;
    project: string;
    pumpStation: string;
    pumpBrand: string;
    operationHr: string;
    operationPower: string;
    pumpHead: string;
    pumpFlowRate: string;
    dischargeP: string;
    mainHeaderP: string;
    suctionP: string;
    fatPumpPower: string;
    ratedMotorPower: string;
    enteredBy?: string;
    // All vibration readings for pump and motor, NDE and DE
    pumpNDE_bv: string;
    pumpNDE_bg: string;
    pumpNDE_accV: string;
    pumpNDE_accH: string;
    pumpNDE_accAxl: string;
    pumpNDE_velV: string;
    pumpNDE_velH: string;
    pumpNDE_velAxl: string;
    pumpNDE_temp: string;
    pumpDE_bv: string;
    pumpDE_bg: string;
    pumpDE_accV: string;
    pumpDE_accH: string;
    pumpDE_accAxl: string;
    pumpDE_velV: string;
    pumpDE_velH: string;
    pumpDE_velAxl: string;
    pumpDE_temp: string;
    motorNDE_bv: string;
    motorNDE_bg: string;
    motorNDE_accV: string;
    motorNDE_accH: string;
    motorNDE_accAxl: string;
    motorNDE_velV: string;
    motorNDE_velH: string;
    motorNDE_velAxl: string;
    motorNDE_temp: string;
    motorDE_bv: string;
    motorDE_bg: string;
    motorDE_accV: string;
    motorDE_accH: string;
    motorDE_accAxl: string;
    motorDE_velV: string;
    motorDE_velH: string;
    motorDE_velAxl: string;
    motorDE_temp: string;
    vibrationRMS: number;
}

export const exampleVibrationHistory: VibrationHistoryRecord[] = [
    // Equipment definitions for 12 unique equipment
    ...(() => {
        const equipmentList = [
            { equipmentId: 'EQ-001', pumpNo: 'P-001', motorBrand: 'ABB', pumpBrand: 'Grundfos', zone: 'A', pumpStation: 'A1', project: 'Main' },
            { equipmentId: 'EQ-002', pumpNo: 'P-002', motorBrand: 'Siemens', pumpBrand: 'Caterpillar', zone: 'B', pumpStation: 'B1', project: 'Expansion' },
            { equipmentId: 'EQ-003', pumpNo: 'P-003', motorBrand: 'GE', pumpBrand: 'GE', zone: 'C', pumpStation: 'C1', project: 'Upgrade' },
            { equipmentId: 'EQ-004', pumpNo: 'P-004', motorBrand: 'ABB', pumpBrand: 'Siemens', zone: 'D', pumpStation: 'D1', project: 'Expansion' },
            { equipmentId: 'EQ-005', pumpNo: 'P-005', motorBrand: 'GE', pumpBrand: 'Grundfos', zone: 'A', pumpStation: 'A1', project: 'Backup' },
            { equipmentId: 'EQ-006', pumpNo: 'P-006', motorBrand: 'ABB', pumpBrand: 'ABB', zone: 'B', pumpStation: 'B1', project: 'Auxiliary' },
            { equipmentId: 'EQ-007', pumpNo: 'P-007', motorBrand: 'GE', pumpBrand: 'Caterpillar', zone: 'C', pumpStation: 'C1', project: 'Backup' },
            { equipmentId: 'EQ-008', pumpNo: 'P-008', motorBrand: 'GE', pumpBrand: 'GE', zone: 'D', pumpStation: 'D1', project: 'Reserve' },
            { equipmentId: 'EQ-009', pumpNo: 'P-009', motorBrand: 'Carrier', pumpBrand: 'Carrier', zone: 'A', pumpStation: 'A1', project: 'HVAC' },
            { equipmentId: 'EQ-010', pumpNo: 'P-010', motorBrand: 'Schneider', pumpBrand: 'Schneider', zone: 'B', pumpStation: 'B1', project: 'Distribution' },
            { equipmentId: 'EQ-011', pumpNo: 'P-011', motorBrand: 'GE', pumpBrand: 'GE', zone: 'C', pumpStation: 'C1', project: 'Transformer' },
            { equipmentId: 'EQ-012', pumpNo: 'P-012', motorBrand: 'TestBrand', pumpBrand: 'TestPump', zone: 'D', pumpStation: 'D1', project: 'Test' },
        ];
        const records: VibrationHistoryRecord[] = [];
        const months = [
            '2023-07', '2023-08', '2023-09', '2023-10', '2023-11', '2023-12',
            '2024-01', '2024-02', '2024-03', '2024-04', '2024-05', '2024-06',
        ];
        // Vibration patterns: low, medium, high, critical
        const vibrationPattern = [1.2, 2.8, 4.5, 7.0, 9.5, 12.2, 14.8, 10.5, 6.0, 3.5, 2.0, 1.0];
        equipmentList.forEach((eq, eqIdx) => {
            months.forEach((month, mIdx) => {
                const date = `${month}-01`;
                const vibrationRMS = vibrationPattern[(mIdx + eqIdx) % vibrationPattern.length];
                records.push({
                    id: `VH-${eq.equipmentId}-${month}`,
                    equipmentId: eq.equipmentId,
                    date,
                    pumpData: { nde: {}, de: {} },
                    motorData: { nde: {}, de: {} },
                    positions: { pLeg1: 'OK', pLeg2: 'OK', pLeg3: 'OK', pLeg4: 'OK', mLeg1: 'OK', mLeg2: 'OK', mLeg3: 'OK', mLeg4: 'OK', vibrationRMS },
                    vibrationRMS,
                    zone: eq.zone,
                    pumpNo: eq.pumpNo,
                    motorBrand: eq.motorBrand,
                    serialNumbers: `SN-${eq.equipmentId}`,
                    project: eq.project,
                    pumpStation: eq.pumpStation,
                    pumpBrand: eq.pumpBrand,
                    operationHr: `${1000 + mIdx * 100}`,
                    operationPower: `${45 + mIdx * 2}`,
                    pumpHead: `${30 + mIdx}`,
                    pumpFlowRate: `${1500 + mIdx * 50}`,
                    dischargeP: `${5 + mIdx}`,
                    mainHeaderP: `${4 + mIdx}`,
                    suctionP: `${2 + mIdx}`,
                    fatPumpPower: `${50 + mIdx * 2}`,
                    ratedMotorPower: `${45 + mIdx * 2}`,
                    enteredBy: `user${eqIdx + 1}`,
                    pumpNDE_bv: '1.0', pumpNDE_bg: '1.0', pumpNDE_accV: '1.0', pumpNDE_accH: '1.0', pumpNDE_accAxl: '1.0', pumpNDE_velV: '1.0', pumpNDE_velH: '1.0', pumpNDE_velAxl: '1.0', pumpNDE_temp: '35',
                    pumpDE_bv: '1.0', pumpDE_bg: '1.0', pumpDE_accV: '1.0', pumpDE_accH: '1.0', pumpDE_accAxl: '1.0', pumpDE_velV: '1.0', pumpDE_velH: '1.0', pumpDE_velAxl: '1.0', pumpDE_temp: '36',
                    motorNDE_bv: '1.0', motorNDE_bg: '1.0', motorNDE_accV: '1.0', motorNDE_accH: '1.0', motorNDE_accAxl: '1.0', motorNDE_velV: '1.0', motorNDE_velH: '1.0', motorNDE_velAxl: '1.0', motorNDE_temp: '34',
                    motorDE_bv: '1.0', motorDE_bg: '1.0', motorDE_accV: '1.0', motorDE_accH: '1.0', motorDE_accAxl: '1.0', motorDE_velV: '1.0', motorDE_velH: '1.0', motorDE_velAxl: '1.0', motorDE_temp: '35',
                });
            });
        });
        return records;
    })()
];

export const initialVibrationHistory: VibrationHistoryRecord[] = exampleVibrationHistory; 